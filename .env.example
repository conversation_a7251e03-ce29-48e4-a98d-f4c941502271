APP_NAME="Your App"
# Disable debug mode by changing APP_DEBUG=false when your site is ready for live.
APP_DEBUG=true
APP_ENV=production
# Must change APP_URL to your website URL. Example: APP_URL=http://your-domain.com
APP_URL=http://localhost
APP_KEY=base64:hMS5VtciEk3t/0Ije8BCRl+AZOvU2gJanbAw5i/LgIs=
LOG_CHANNEL=daily

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_CLIENT=predis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Change to your database credentials
DB_CONNECTION=mysql
# If you use Laravel Sail, just change DB_HOST to DB_HOST=mysql
# On some hosting DB_HOST can be localhost instead of 127.0.0.1
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE="laravel"
DB_USERNAME="root"
DB_PASSWORD="your_db_password"
DB_STRICT=false

# You should change this value to hide your admin URL (DON'T use special characters). Example: ADMIN_DIR=hello-admin then your admin URL will be
# http://your-domain.com/hello-admin
ADMIN_DIR=admin

CMS_ENABLE_INSTALLER=true
