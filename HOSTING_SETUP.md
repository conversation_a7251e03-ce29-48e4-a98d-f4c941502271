# 🚀 تعليمات رفع ArtBella على الاستضافة المشتركة

## 📦 ما تم تحضيره:
✅ جميع التبعيات مثبتة
✅ قاعدة البيانات جاهزة (migrations تمت)
✅ الأصول منشورة
✅ الإعدادات محسنة للإنتاج
✅ الصلاحيات مضبوطة

## 🔧 خطوات الرفع:

### 1. رفع الملفات
- ارفع جميع الملفات إلى public_html
- أو ارفع إلى مجلد فرعي ثم انقل محتويات public إلى public_html

### 2. إعداد قاعدة البيانات في Hostinger
```env
# حدّث هذه القيم في ملف .env:
DB_HOST=localhost
DB_DATABASE=u651699483_art
DB_USERNAME=u651699483_user
DB_PASSWORD=your_password_from_hostinger
```

### 3. إعد<PERSON> Document Root
**الطريقة الأولى (الأفضل):**
- في cPanel، اضبط Document Root على: public_html/public

**الطريقة الثانية:**
- انقل محتويات مجلد public إلى public_html
- احذف مجلد public الفارغ

### 4. اختبار الموقع
- الصفحة الرئيسية: https://artbella.online/
- لوحة الإدارة: https://artbella.online/admin

## ⚠️ ملاحظات مهمة:
- PHP 8.1+ مطلوب
- mod_rewrite يجب أن يكون مفعل
- جميع extensions PHP مثبتة
- قاعدة البيانات جاهزة (لا تحتاج migrate)

## 🆘 في حالة المشاكل:
1. تحقق من سجلات الأخطاء في storage/logs/
2. تأكد من إعدادات قاعدة البيانات
3. تحقق من Document Root
4. تأكد من الصلاحيات (777 لـ storage و bootstrap/cache)
