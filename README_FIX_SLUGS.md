# إصلاح مشكلة الـ Slugs في نظام الدورات التدريبية

هذا الملف يشرح كيفية إصلاح مشكلة الـ slugs في جداول الدورات التدريبية وفئات الدورات التدريبية.

## المشكلة

تم اكتشاف أن بعض الدورات التدريبية وفئات الدورات قد لا تحتوي على قيم في حقل `slug` مما يؤدي إلى مشاكل في عرض الروابط والتنقل في الموقع.

## الحلول المتاحة

هناك عدة طرق لإصلاح هذه المشكلة:

### 1. تنفيذ ملف SQL مباشرة

يمكنك تنفيذ ملف `fix_course_slugs.sql` مباشرة في قاعدة البيانات باستخدام أي أداة إدارة قواعد البيانات مثل phpMyAdmin:

1. افتح phpMyAdmin على الرابط: http://localhost/phpmyadmin/
2. اختر قاعدة البيانات `art`
3. انقر على علامة التبويب "SQL"
4. انسخ محتوى ملف `fix_course_slugs.sql` والصقه في مربع النص
5. انقر على "تنفيذ"

### 2. تنفيذ ملف PHP

يمكنك تنفيذ أحد ملفات PHP التالية:

- `fix_slugs.php`: يقوم بإصلاح الـ slugs لجميع الدورات وفئات الدورات
- `update_course_slugs.php`: يقوم بإصلاح الـ slugs للدورات فقط
- `execute_fix_slugs.php`: يقوم بتنفيذ ملف SQL لإصلاح الـ slugs

لتنفيذ أي من هذه الملفات، استخدم الأمر التالي في سطر الأوامر:

```bash
php c:\xampp\htdocs\arttt\fix_slugs.php
```

أو

```bash
php c:\xampp\htdocs\arttt\update_course_slugs.php
```

أو

```bash
php c:\xampp\htdocs\arttt\execute_fix_slugs.php
```

### 3. تنفيذ الهجرات

يمكنك تنفيذ الهجرات المعلقة باستخدام الأمر التالي:

```bash
php artisan migrate
```

هذا سيقوم بتنفيذ جميع ملفات الهجرة المعلقة بما في ذلك:
- `2025_05_26_add_slug_to_courses_table.php`
- `2025_05_26_add_slug_to_course_categories_table_if_not_exists.php`

## التحقق من الإصلاح

بعد تنفيذ أي من الحلول أعلاه، يمكنك التحقق من نجاح الإصلاح عن طريق تنفيذ الاستعلامات التالية في phpMyAdmin:

```sql
-- التحقق من وجود حقل slug في جدول ta_courses
SHOW COLUMNS FROM ta_courses LIKE 'slug';

-- عرض عدد الدورات التي تحتوي على slug
SELECT COUNT(*) FROM ta_courses WHERE slug IS NOT NULL;

-- التحقق من وجود حقل slug في جدول ta_course_categories
SHOW COLUMNS FROM ta_course_categories LIKE 'slug';

-- عرض عدد فئات الدورات التي تحتوي على slug
SELECT COUNT(*) FROM ta_course_categories WHERE slug IS NOT NULL;
```

## ملاحظات هامة

1. تأكد من أن نماذج `Course` و `CourseCategory` تحتوي على حقل `slug` في مصفوفة `$fillable` (تم التحقق من ذلك وتأكيد وجوده).
2. تأكد من أن الدالة `boot()` في النماذج تقوم بإنشاء الـ slug تلقائيًا عند إنشاء أو تحديث السجلات (تم التحقق من ذلك وتأكيد وجوده).
3. تأكد من أن القوالب تستخدم الـ slug في الروابط بشكل صحيح.

## معلومات الاتصال بقاعدة البيانات

- اسم قاعدة البيانات: `art`
- اسم المستخدم: `root`
- كلمة المرور: (فارغة)
- المضيف: `127.0.0.1`
- المنفذ: `3306`
