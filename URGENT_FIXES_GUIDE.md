# دليل الإصلاحات العاجلة - ArtBella
## 🚨 حلول فورية للمشاكل الحرجة

### 📅 تاريخ الإنشاء: 2 يوليو 2025
### ⏰ الأولوية: عاجلة جداً

---

## 🔥 الإصلاحات الفورية (يجب تنفيذها خلال 24 ساعة)

### 1. إصلاح مشكلة Debug في الإنتاج

```bash
# تعديل ملف .env
sed -i 's/APP_DEBUG=true/APP_DEBUG=false/' .env

# مسح الكاش
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

### 2. إصلاح الروت المفقود

```php
// إضافة في platform/plugins/marketplace/routes/admin.php
Route::group(['prefix' => 'commission-management', 'as' => 'commission-management.'], function () {
    Route::get('/', 'CommissionManagementController@index')->name('index');
    Route::get('/dashboard-stats', 'CommissionManagementController@dashboardStats')->name('dashboard-stats');
    Route::get('/reports', 'CommissionManagementController@reports')->name('reports');
    Route::get('/vendor-settings', 'CommissionManagementController@vendorSettings')->name('vendor-settings');
});
```

### 3. إنشاء View المفقود

```php
// إنشاء ملف: platform/plugins/marketplace/resources/views/commission-management/index.blade.php
@extends('core/base::layouts.master')

@section('content')
<div class="main-content">
    <div class="page-header">
        <h1>إدارة العمولات</h1>
    </div>
    
    <div class="page-content">
        <div class="row">
            <div class="col-md-12">
                <div class="widget-box">
                    <div class="widget-header">
                        <h4>لوحة تحكم العمولات</h4>
                    </div>
                    <div class="widget-body">
                        <p>مرحباً بك في نظام إدارة العمولات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
```

### 4. إصلاح جدول قاعدة البيانات المفقود

```sql
-- تشغيل هذا الاستعلام في قاعدة البيانات
CREATE TABLE IF NOT EXISTS `mp_service_types` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `slug` varchar(255) NOT NULL,
    `description` text,
    `default_commission_rate` decimal(5,2) DEFAULT 5.00,
    `status` varchar(60) NOT NULL DEFAULT 'published',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `mp_service_types_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج البيانات الافتراضية
INSERT INTO `mp_service_types` (`name`, `slug`, `description`, `default_commission_rate`) VALUES
('المنتجات', 'products', 'عمولة بيع المنتجات العادية', 3.00),
('الدورات التدريبية', 'courses', 'عمولة الدورات التعليمية', 5.00),
('الخدمات', 'services', 'عمولة الخدمات المختلفة', 4.00),
('المواعيد والحجوزات', 'appointments', 'عمولة حجز المواعيد', 2.50),
('الاستشارات', 'consultations', 'عمولة الاستشارات الجديدة', 6.00);
```

### 5. إصلاح مشكلة SelectFieldOption

```php
// في platform/plugins/marketplace/src/Providers/HookServiceProvider.php
// استبدال السطر 304
// من:
->placeholder('اختر نوع الخدمة')

// إلى:
->addAttribute('placeholder', 'اختر نوع الخدمة')
```

---

## ⚡ الإصلاحات السريعة (يجب تنفيذها خلال 48 ساعة)

### 6. إصلاح مشكلة المتغير المفقود

```php
// في platform/plugins/marketplace/src/Http/Controllers/VendorConsultationController.php
public function create()
{
    // إضافة هذا السطر
    $services = $this->serviceRepository->allBy(['store_id' => auth('customer')->user()->store->id]);
    
    return view('marketplace::vendor-dashboard.consultations.create', compact('services'));
}
```

### 7. إصلاح CheckReelPermissions Middleware

```php
// في platform/plugins/vendor-reels/src/Http/Middleware/CheckReelPermissions.php
// تغيير نوع الإرجاع في السطر 23
public function handle($request, Closure $next): Response|RedirectResponse
{
    // باقي الكود...
    
    // تغيير السطر 40 من:
    if ($user->store->status->value !== 'published') {
    
    // إلى:
    if ($user->store->status !== 'published') {
    
    // وتغيير الإرجاع من:
    return response()->json(['error' => 'Unauthorized'], 403);
    
    // إلى:
    return redirect()->back()->with('error', 'غير مصرح لك بالوصول');
}
```

### 8. إصلاح VendorReelController Constructor

```php
// في routes/web.php - تصحيح السطر 93
// من:
$controller = new \Botble\VendorReels\Http\Controllers\VendorReelController();

// إلى:
$vendorReelRepository = app(\Botble\VendorReels\Repositories\Interfaces\VendorReelInterface::class);
$controller = new \Botble\VendorReels\Http\Controllers\VendorReelController($vendorReelRepository);
```

### 9. إضافة الدالة المفقودة في CommissionManagementController

```php
// في platform/plugins/marketplace/src/Http/Controllers/CommissionManagementController.php
public function createServiceType(Request $request)
{
    $serviceType = new ServiceType();
    $serviceType->name = $request->input('name');
    $serviceType->slug = Str::slug($request->input('name'));
    $serviceType->description = $request->input('description');
    $serviceType->default_commission_rate = $request->input('default_commission_rate', 5.00);
    $serviceType->save();
    
    return response()->json(['success' => true, 'message' => 'تم إنشاء نوع الخدمة بنجاح']);
}
```

---

## 🔧 سكريبت الإصلاح الآلي

```bash
#!/bin/bash
# ملف: urgent_fixes.sh

echo "🚀 بدء تطبيق الإصلاحات العاجلة..."

# 1. إيقاف Debug
echo "📝 إيقاف Debug في الإنتاج..."
sed -i 's/APP_DEBUG=true/APP_DEBUG=false/' .env

# 2. مسح الكاش
echo "🧹 مسح الكاش..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# 3. إنشاء جدول قاعدة البيانات
echo "🗃️ إنشاء جدول mp_service_types..."
php artisan tinker --execute="
DB::statement('CREATE TABLE IF NOT EXISTS mp_service_types (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    slug varchar(255) NOT NULL,
    description text,
    default_commission_rate decimal(5,2) DEFAULT 5.00,
    status varchar(60) NOT NULL DEFAULT \"published\",
    created_at timestamp NULL DEFAULT NULL,
    updated_at timestamp NULL DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY mp_service_types_slug_unique (slug)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
"

# 4. إدراج البيانات الافتراضية
echo "📊 إدراج البيانات الافتراضية..."
php artisan tinker --execute="
DB::table('mp_service_types')->insertOrIgnore([
    ['name' => 'المنتجات', 'slug' => 'products', 'description' => 'عمولة بيع المنتجات العادية', 'default_commission_rate' => 3.00],
    ['name' => 'الدورات التدريبية', 'slug' => 'courses', 'description' => 'عمولة الدورات التعليمية', 'default_commission_rate' => 5.00],
    ['name' => 'الخدمات', 'slug' => 'services', 'description' => 'عمولة الخدمات المختلفة', 'default_commission_rate' => 4.00],
    ['name' => 'المواعيد والحجوزات', 'slug' => 'appointments', 'description' => 'عمولة حجز المواعيد', 'default_commission_rate' => 2.50],
    ['name' => 'الاستشارات', 'slug' => 'consultations', 'description' => 'عمولة الاستشارات الجديدة', 'default_commission_rate' => 6.00]
]);
"

# 5. إعادة تحميل الكاش
echo "🔄 إعادة تحميل الكاش..."
php artisan config:cache
php artisan route:cache

echo "✅ تم تطبيق جميع الإصلاحات العاجلة بنجاح!"
echo "🔍 يرجى اختبار النظام للتأكد من عمل الإصلاحات"
```

---

## 📋 قائمة التحقق بعد الإصلاح

### ✅ اختبارات يجب إجراؤها:

1. **اختبار الروتس**:
   ```bash
   curl -I http://localhost:8000/admin/commission-management
   ```

2. **اختبار قاعدة البيانات**:
   ```sql
   SELECT COUNT(*) FROM mp_service_types;
   ```

3. **اختبار العرض**:
   - زيارة `/admin/commission-management`
   - التأكد من عدم ظهور أخطاء

4. **اختبار السجلات**:
   ```bash
   tail -f storage/logs/laravel-$(date +%Y-%m-%d).log
   ```

### 🚨 علامات الخطر:
- ظهور أخطاء 500
- رسائل خطأ في السجلات
- عدم تحميل الصفحات
- مشاكل في قاعدة البيانات

---

## 📞 الدعم الفني

### في حالة فشل الإصلاحات:
1. **إيقاف الموقع مؤقتاً**: `php artisan down`
2. **استعادة النسخة الاحتياطية**: إذا متوفرة
3. **التواصل مع فريق التطوير**: فوراً
4. **توثيق الخطأ**: لتجنب تكراره

### معلومات الاتصال الطارئ:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +20-XXX-XXXX
- **Slack**: #emergency-fixes

---

*تم إنشاء هذا الدليل في 2 يوليو 2025 - للاستخدام الفوري*
