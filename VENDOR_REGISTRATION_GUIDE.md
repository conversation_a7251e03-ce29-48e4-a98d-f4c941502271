# 🏪 دليل تسجيل البائعين مع فئات العمل

## 📋 نظرة عامة

تم تطوير نظام تسجيل البائعين ليتضمن:
- ✅ **اختيار فئة العمل** من قائمة منسدلة
- ✅ **حقول معلومات المتجر** (الاسم، الهاتف، العنوان)
- ✅ **تحديد الموقع** باستخدام Google Maps
- ✅ **إنشاء متجر تلقائياً** عند التسجيل

---

## 🎯 كيفية التسجيل كبائع

### الخطوة 1: الوصول لصفحة التسجيل
اذهب إلى: `yourdomain.com/register`

### الخطوة 2: اختيار نوع الحساب
ستجد خيارين:
- ⭕ **أنا عميل** (I am a customer)
- ⭕ **أنا بائع** (I am a vendor)

اختر **"أنا بائع"**

### الخطوة 3: ملء البيانات الأساسية
```
📝 الاسم الكامل: [مطلوب]
📧 البريد الإلكتروني: [مطلوب]
📱 رقم الهاتف: [اختياري]
🔒 كلمة المرور: [مطلوب]
🔒 تأكيد كلمة المرور: [مطلوب]
```

### الخطوة 4: اختيار فئة العمل
ستظهر قائمة منسدلة تحتوي على:

🏋️ **صالات رياضية ولياقة بدنية**
- صالات الجيم
- مراكز اللياقة البدنية
- استوديوهات اليوغا
- مراكز الفنون القتالية

🏥 **عيادات طبية**
- عيادات الأطباء
- مراكز الأسنان
- عيادات التجميل الطبي
- مراكز العلاج الطبيعي

✂️ **صالونات رجالية**
- صالونات الحلاقة
- مراكز العناية بالرجال
- صالونات التجميل الرجالية

💄 **صالونات تجميل**
- صالونات النساء
- مراكز التجميل
- صالونات الأظافر
- مراكز العناية بالبشرة

🎓 **مراكز تدريب**
- معاهد التدريب المهني
- مراكز تعليم اللغات
- مراكز التدريب التقني
- أكاديميات التعليم

💼 **خدمات أخرى**
- أي خدمات أخرى لا تندرج تحت الفئات السابقة

### الخطوة 5: معلومات المتجر
```
🏪 اسم المتجر: [مطلوب]
📞 رقم هاتف المتجر: [مطلوب]
```

### الخطوة 6: تحديد الموقع
```
📍 عنوان المتجر: [مطلوب]
🌍 المدينة: [يُملأ تلقائياً]
🗺️ المحافظة/الولاية: [يُملأ تلقائياً]
🌎 البلد: [يُملأ تلقائياً]
```

**ميزات تحديد الموقع:**
- 🔍 **البحث التلقائي**: ابدأ بكتابة العنوان وستظهر اقتراحات من Google Maps
- 📍 **الموقع الحالي**: اضغط زر "استخدام موقعي الحالي"
- 🗺️ **الإحداثيات**: تُحفظ تلقائياً (خط الطول والعرض)

---

## 🖥️ شكل النموذج

```
┌─────────────────────────────────────────┐
│           تسجيل حساب جديد               │
├─────────────────────────────────────────┤
│ الاسم الكامل: [________________]       │
│ البريد الإلكتروني: [________________] │
│ رقم الهاتف: [________________]         │
│ كلمة المرور: [________________]        │
│ تأكيد كلمة المرور: [________________]  │
│                                         │
│ نوع الحساب:                            │
│ ○ أنا عميل    ● أنا بائع               │
│                                         │
│ ┌─── معلومات البائع ───────────────┐   │
│ │ فئة العمل: [▼ اختر فئة العمل]    │   │
│ │ اسم المتجر: [________________]   │   │
│ │ رقم هاتف المتجر: [________________] │   │
│ │                                   │   │
│ │ عنوان المتجر: [________________] │   │
│ │ [📍 استخدام موقعي الحالي]        │   │
│ │                                   │   │
│ │ المدينة: [________________]       │   │
│ │ المحافظة: [________________]      │   │
│ │ البلد: [________________]         │   │
│ └───────────────────────────────────┘   │
│                                         │
│ ☑ أوافق على الشروط والأحكام            │
│                                         │
│        [تسجيل الحساب]                   │
└─────────────────────────────────────────┘
```

---

## 🔧 للمطورين

### الحقول المضافة في قاعدة البيانات

#### جدول العملاء (ec_customers):
```sql
- address (varchar)
- city (varchar) 
- state (varchar)
- country (varchar)
- latitude (decimal)
- longitude (decimal)
```

#### جدول المتاجر (mp_stores):
```sql
- vendor_category_id (foreign key)
- latitude (decimal)
- longitude (decimal)
- address (varchar)
- city (varchar)
- state (varchar) 
- country (varchar)
```

#### جدول فئات البائعين (vendor_categories):
```sql
- id (primary key)
- name (varchar)
- name_ar (varchar)
- slug (varchar)
- description (text)
- description_ar (text)
- icon (varchar)
- color (varchar)
- is_active (boolean)
- sort_order (integer)
```

### API Endpoints

```php
// الحصول على فئات البائعين
GET /api/v1/location/vendor-categories

// البحث عن المتاجر حسب الفئة
GET /api/v1/location/stores/nearby?category_id=1

// البحث عن الخدمات حسب الفئة والموقع
GET /api/v1/location/services/nearby?category_id=1&latitude=24.7136&longitude=46.6753
```

### JavaScript Events

```javascript
// عند اختيار "أنا بائع"
$('input[name=is_vendor]').on('change', function() {
    if ($(this).val() == 1) {
        $('.vendor-info').slideDown();
        // تفعيل Google Maps
        new GoogleMapsGeocodingEnhanced();
    }
});

// عند اختيار فئة البائع
$('select[name=vendor_category_id]').on('change', function() {
    // إضافة تأثيرات بصرية
    $(this).addClass('category-selected');
});
```

---

## 🎨 التخصيص

### تخصيص الألوان حسب الفئة

```css
.category-gym { background-color: #28a745; }      /* أخضر للجيم */
.category-clinic { background-color: #dc3545; }   /* أحمر للعيادات */
.category-salon { background-color: #6f42c1; }    /* بنفسجي للصالونات */
.category-beauty { background-color: #e83e8c; }   /* وردي للتجميل */
.category-training { background-color: #fd7e14; } /* برتقالي للتدريب */
.category-other { background-color: #6c757d; }    /* رمادي للأخرى */
```

### إضافة فئة جديدة

```php
VendorCategory::create([
    'name' => 'New Category',
    'name_ar' => 'فئة جديدة',
    'slug' => 'new-category',
    'description' => 'Description in English',
    'description_ar' => 'الوصف بالعربية',
    'icon' => 'fas fa-icon',
    'color' => '#007bff',
    'is_active' => true,
    'sort_order' => 10
]);
```

---

## 🚀 الميزات المتقدمة

### 1. البحث الذكي
- البحث عن البائعين حسب الفئة والموقع
- ترتيب النتائج حسب المسافة
- فلترة حسب التقييمات والشعبية

### 2. الإحصائيات
- عدد البائعين في كل فئة
- التوزيع الجغرافي للمتاجر
- أكثر الفئات طلباً

### 3. التكامل مع Google Maps
- عرض المتاجر على الخريطة
- الاتجاهات إلى المتجر
- معلومات المرور والمسافة

---

## 🔍 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. لا تظهر فئات البائعين
**السبب**: لم يتم تشغيل migration فئات البائعين
**الحل**: 
```bash
php artisan migrate
```

#### 2. لا تظهر حقول البائع عند الاختيار
**السبب**: JavaScript غير محمل
**الحل**: تأكد من تحميل ملف `customer-register.js`

#### 3. خطأ في حفظ الموقع
**السبب**: Google Maps API غير مفعل
**الحل**: تأكد من إعداد API Key وتفعيل Geocoding API

---

## 📞 الدعم

للحصول على المساعدة:
1. راجع ملف `test_vendor_registration_form.php` للاختبار
2. تحقق من ملفات الـ logs في `storage/logs/`
3. استخدم أدوات المطور في المتصفح لفحص JavaScript

---

**🎉 مبروك! نظام تسجيل البائعين مع فئات العمل جاهز للاستخدام**

الآن يمكن للبائعين التسجيل بسهولة واختيار فئة عملهم، وسيظهرون في نتائج البحث الجغرافي حسب فئتهم وموقعهم.
