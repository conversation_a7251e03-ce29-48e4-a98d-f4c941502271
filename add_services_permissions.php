<?php

echo "🔐 إضافة أذونات الخدمات للنظام\n\n";

// إعداد Laravel
$autoload = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoload)) {
    require_once $autoload;
    
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} else {
    echo "❌ لا يمكن العثور على autoload.php\n";
    exit;
}

// إضافة أذونات الخدمات
echo "1. إضافة أذونات الخدمات:\n";

$permissions = [
    ['name' => 'عرض الخدمات', 'slug' => 'services.index'],
    ['name' => 'إنشاء خدمة', 'slug' => 'services.create'],
    ['name' => 'تعديل خدمة', 'slug' => 'services.edit'],
    ['name' => 'حذف خدمة', 'slug' => 'services.destroy'],
];

foreach ($permissions as $perm) {
    try {
        $permission = \Botble\Base\Models\Permission::firstOrCreate(
            ['slug' => $perm['slug']],
            ['name' => $perm['name'], 'is_feature' => 0]
        );
        echo "  ✅ تم إضافة إذن: {$perm['name']} ({$perm['slug']})\n";
    } catch (Exception $e) {
        echo "  ❌ خطأ في إضافة إذن {$perm['name']}: " . $e->getMessage() . "\n";
    }
}

// إضافة الأذونات للسوبر أدمن
echo "\n2. إضافة الأذونات للسوبر أدمن:\n";

try {
    $superAdminRole = \Botble\Base\Models\Role::find(1);
    
    if ($superAdminRole) {
        foreach ($permissions as $perm) {
            $permission = \Botble\Base\Models\Permission::where('slug', $perm['slug'])->first();
            if ($permission && !$superAdminRole->permissions->contains($permission->id)) {
                $superAdminRole->permissions()->attach($permission->id);
                echo "  ✅ تم إضافة إذن للسوبر أدمن: {$perm['name']}\n";
            } else {
                echo "  ℹ️  الإذن موجود بالفعل للسوبر أدمن: {$perm['name']}\n";
            }
        }
    } else {
        echo "  ❌ لم يتم العثور على دور السوبر أدمن\n";
    }
} catch (Exception $e) {
    echo "  ❌ خطأ في إضافة الأذونات للسوبر أدمن: " . $e->getMessage() . "\n";
}

// التحقق من النتائج
echo "\n3. التحقق من النتائج:\n";

try {
    $servicePermissions = \Botble\Base\Models\Permission::where('slug', 'like', '%service%')->get();
    echo "  📊 إجمالي أذونات الخدمات: " . $servicePermissions->count() . "\n";
    
    foreach ($servicePermissions as $permission) {
        echo "    • {$permission->name} ({$permission->slug})\n";
    }
    
    // فحص أذونات السوبر أدمن
    $superAdminRole = \Botble\Base\Models\Role::find(1);
    if ($superAdminRole) {
        $adminServicePermissions = $superAdminRole->permissions()
            ->where('slug', 'like', '%service%')
            ->get();
        echo "\n  👑 أذونات الخدمات للسوبر أدمن: " . $adminServicePermissions->count() . "\n";
        
        foreach ($adminServicePermissions as $permission) {
            echo "    • {$permission->name}\n";
        }
    }
    
} catch (Exception $e) {
    echo "  ❌ خطأ في التحقق من النتائج: " . $e->getMessage() . "\n";
}

echo "\n✅ تم الانتهاء من إضافة أذونات الخدمات بنجاح!\n\n";

echo "🎯 الآن يمكن للسوبر أدمن:\n";
echo "  ✅ عرض قائمة الخدمات\n";
echo "  ✅ إنشاء خدمات جديدة\n";
echo "  ✅ تعديل الخدمات الموجودة\n";
echo "  ✅ حذف الخدمات\n\n";

echo "🌟 الخطوة التالية: تشغيل add_vendor_beauty_services.php لإضافة خدمات العناية والجمال\n";
