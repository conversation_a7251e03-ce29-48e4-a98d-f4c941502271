-- إضافة أذونات الخدمات للنظام

-- 1. إضافة أذونات الخدمات
INSERT IGNORE INTO permissions (name, slug, is_feature, created_at, updated_at) VALUES 
('عرض الخدمات', 'services.index', 0, NOW(), NOW()),
('إنشاء خدمة', 'services.create', 0, NOW(), NOW()),
('تعديل خدمة', 'services.edit', 0, NOW(), NOW()),
('حذف خدمة', 'services.destroy', 0, NOW(), NOW());

-- 2. إضافة الأذونات لدور المدير العام (Super Admin)
INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at, updated_at)
SELECT 1, p.id, NOW(), NOW() 
FROM permissions p 
WHERE p.slug IN (
    'services.index', 'services.create', 'services.edit', 'services.destroy'
);

-- 3. التأكد من أن المستخدم الأول هو Super Admin
UPDATE users SET super_user = 1 WHERE id = 1;

-- 4. إضافة المستخدم الأول لدور المدير العام إذا لم يكن موجوداً
INSERT IGNORE INTO role_users (user_id, role_id, created_at, updated_at) 
VALUES (1, 1, NOW(), NOW());

-- 5. التحقق من النتائج
SELECT 'أذونات الخدمات المضافة:' as status;
SELECT p.name, p.slug FROM permissions p WHERE p.slug LIKE '%service%';

SELECT 'أذونات المدير العام للخدمات:' as status;
SELECT p.name, p.slug 
FROM permissions p 
JOIN role_permissions rp ON p.id = rp.permission_id 
WHERE rp.role_id = 1 AND p.slug LIKE '%service%';
