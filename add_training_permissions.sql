-- إضافة أذونات التدريب والمواعيد إلى قاعدة البيانات
-- تشغيل هذا في قاعدة البيانات MySQL مباشرة

-- 1. إضافة الأذونات الرئيسية
INSERT IGNORE INTO permissions (name, slug, is_feature, created_at, updated_at) VALUES 
('التدريب والمواعيد', 'training-appointment.index', 0, NOW(), NOW()),
('عرض الدورات', 'courses.index', 0, NOW(), NOW()),
('إنشاء دورة', 'courses.create', 0, NOW(), NOW()),
('تعديل دورة', 'courses.edit', 0, NOW(), NOW()),
('حذف دورة', 'courses.destroy', 0, NOW(), NOW()),
('عرض تصنيفات الدورات', 'course-categories.index', 0, NOW(), NOW()),
('إنشاء تصنيف دورة', 'course-categories.create', 0, NOW(), NOW()),
('تعديل تصنيف دورة', 'course-categories.edit', 0, NOW(), NOW()),
('حذف تصنيف دورة', 'course-categories.destroy', 0, NOW(), NOW()),
('عرض دروس الدورات', 'course-lessons.index', 0, NOW(), NOW()),
('إنشاء درس', 'course-lessons.create', 0, NOW(), NOW()),
('تعديل درس', 'course-lessons.edit', 0, NOW(), NOW()),
('حذف درس', 'course-lessons.destroy', 0, NOW(), NOW()),
('عرض المواعيد', 'appointments.index', 0, NOW(), NOW()),
('إنشاء موعد', 'appointments.create', 0, NOW(), NOW()),
('تعديل موعد', 'appointments.edit', 0, NOW(), NOW()),
('حذف موعد', 'appointments.destroy', 0, NOW(), NOW());

-- 2. إضافة الأذونات لدور المدير العام (Super Admin)
INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at, updated_at)
SELECT 1, p.id, NOW(), NOW() 
FROM permissions p 
WHERE p.slug IN (
    'training-appointment.index',
    'courses.index', 'courses.create', 'courses.edit', 'courses.destroy',
    'course-categories.index', 'course-categories.create', 'course-categories.edit', 'course-categories.destroy',
    'course-lessons.index', 'course-lessons.create', 'course-lessons.edit', 'course-lessons.destroy',
    'appointments.index', 'appointments.create', 'appointments.edit', 'appointments.destroy'
);

-- 3. التأكد من أن المستخدم الأول هو Super Admin
UPDATE users SET super_user = 1 WHERE id = 1;

-- 4. إضافة المستخدم الأول لدور المدير العام إذا لم يكن موجوداً
INSERT IGNORE INTO role_users (user_id, role_id, created_at, updated_at) 
VALUES (1, 1, NOW(), NOW());

-- 5. التحقق من النتائج
SELECT 'الأذونات المضافة:' as status;
SELECT p.name, p.slug FROM permissions p WHERE p.slug LIKE '%training%' OR p.slug LIKE '%course%' OR p.slug LIKE '%appointment%';

SELECT 'أذونات المدير العام:' as status;
SELECT p.name, p.slug 
FROM permissions p 
JOIN role_permissions rp ON p.id = rp.permission_id 
WHERE rp.role_id = 1 AND (p.slug LIKE '%training%' OR p.slug LIKE '%course%' OR p.slug LIKE '%appointment%');