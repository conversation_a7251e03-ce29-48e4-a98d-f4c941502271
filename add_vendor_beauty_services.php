<?php

echo "💅 إضافة خدمات العناية والجمال للفيندور\n\n";

// إعداد Laravel
$autoload = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoload)) {
    require_once $autoload;
    
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} else {
    echo "❌ لا يمكن العثور على autoload.php\n";
    exit;
}

// 1. البحث عن متجر للفيندور أو إنشاء واحد
echo "1. البحث عن متجر للفيندور:\n";
try {
    $store = \Botble\Marketplace\Models\Store::first();
    
    if (!$store) {
        // إنشاء متجر تجريبي
        $store = \Botble\Marketplace\Models\Store::create([
            'name' => 'صالون آرت بيلا للعناية والجمال',
            'description' => 'صالون متخصص في جميع خدمات العناية والجمال',
            'content' => 'نقدم أفضل خدمات العناية والجمال بأيدي خبراء متخصصين',
            'status' => 'published',
            'customer_id' => 1,
            'phone' => '+966501234567',
            'email' => '<EMAIL>',
            'address' => 'الرياض، حي النخيل',
            'city' => 'الرياض',
            'state' => 'الرياض',
            'country' => 'SA',
            'zip_code' => '12345',
        ]);
        echo "  ✅ تم إنشاء متجر جديد: {$store->name}\n";
    } else {
        echo "  ✅ تم العثور على متجر: {$store->name}\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ خطأ في إنشاء/العثور على المتجر: " . $e->getMessage() . "\n";
    exit;
}

// 2. إضافة خدمات العناية والجمال للمتجر
echo "\n2. إضافة خدمات العناية والجمال:\n";

$beautyServices = [
    // خدمات المكياج
    [
        'name' => 'مكياج يومي طبيعي',
        'description' => 'مكياج خفيف وطبيعي مناسب للاستخدام اليومي',
        'category' => 'مكياج',
        'price' => 150,
        'duration' => 45,
        'is_featured' => true,
    ],
    [
        'name' => 'مكياج سهرة فاخر',
        'description' => 'مكياج احترافي للمناسبات والسهرات الخاصة',
        'category' => 'مكياج',
        'price' => 300,
        'duration' => 90,
        'is_featured' => true,
    ],
    [
        'name' => 'مكياج العروس الكامل',
        'description' => 'مكياج عروس شامل مع التجربة المسبقة',
        'category' => 'مكياج',
        'price' => 800,
        'duration' => 150,
        'is_featured' => true,
    ],
    
    // خدمات العناية بالبشرة
    [
        'name' => 'تنظيف بشرة عميق كلاسيكي',
        'description' => 'تنظيف عميق للبشرة مع التقشير والترطيب',
        'category' => 'عناية بالبشرة',
        'price' => 200,
        'duration' => 75,
        'is_featured' => true,
    ],
    [
        'name' => 'فيشل مضاد للشيخوخة',
        'description' => 'علاج متقدم لمحاربة علامات التقدم في السن',
        'category' => 'عناية بالبشرة',
        'price' => 350,
        'duration' => 90,
        'is_featured' => false,
    ],
    [
        'name' => 'ماسك الذهب الفاخر',
        'description' => 'ماسك ذهبي لإشراق البشرة ونعومتها',
        'category' => 'عناية بالبشرة',
        'price' => 500,
        'duration' => 60,
        'is_featured' => true,
    ],
    [
        'name' => 'علاج حب الشباب',
        'description' => 'علاج متخصص لحب الشباب وآثاره',
        'category' => 'عناية بالبشرة',
        'price' => 250,
        'duration' => 60,
        'is_featured' => false,
    ],
    
    // خدمات العناية بالشعر
    [
        'name' => 'قص وتصفيف شعر نسائي',
        'description' => 'قص وتصفيف احترافي مع استشارة مجانية',
        'category' => 'عناية بالشعر',
        'price' => 180,
        'duration' => 90,
        'is_featured' => false,
    ],
    [
        'name' => 'صبغة شعر طبيعية',
        'description' => 'صبغة شعر بمواد طبيعية آمنة',
        'category' => 'عناية بالشعر',
        'price' => 400,
        'duration' => 180,
        'is_featured' => false,
    ],
    [
        'name' => 'علاج الشعر بالكيراتين',
        'description' => 'علاج مكثف لفرد وتنعيم الشعر',
        'category' => 'عناية بالشعر',
        'price' => 600,
        'duration' => 240,
        'is_featured' => true,
    ],
    [
        'name' => 'حمام كريم وترطيب',
        'description' => 'حمام كريم مغذي لترطيب الشعر الجاف',
        'category' => 'عناية بالشعر',
        'price' => 120,
        'duration' => 60,
        'is_featured' => false,
    ],
    
    // خدمات العناية بالأظافر
    [
        'name' => 'مانيكير كلاسيكي',
        'description' => 'عناية كاملة بأظافر اليدين مع الطلاء',
        'category' => 'عناية بالأظافر',
        'price' => 80,
        'duration' => 45,
        'is_featured' => false,
    ],
    [
        'name' => 'بديكير فاخر',
        'description' => 'عناية شاملة بأظافر القدمين مع التدليك',
        'category' => 'عناية بالأظافر',
        'price' => 100,
        'duration' => 60,
        'is_featured' => false,
    ],
    [
        'name' => 'نيل آرت احترافي',
        'description' => 'رسم احترافي على الأظافر بتصاميم مختلفة',
        'category' => 'عناية بالأظافر',
        'price' => 150,
        'duration' => 75,
        'is_featured' => true,
    ],
    [
        'name' => 'تركيب أظافر جل',
        'description' => 'تركيب أظافر جل طبيعية المظهر',
        'category' => 'عناية بالأظافر',
        'price' => 200,
        'duration' => 120,
        'is_featured' => false,
    ],
    
    // خدمات إزالة الشعر
    [
        'name' => 'إزالة شعر الوجه بالليزر',
        'description' => 'إزالة شعر الوجه بتقنية الليزر الآمنة',
        'category' => 'إزالة الشعر',
        'price' => 300,
        'duration' => 30,
        'is_featured' => false,
    ],
    [
        'name' => 'إزالة شعر الجسم بالليزر',
        'description' => 'إزالة شعر الجسم بالليزر المتطور',
        'category' => 'إزالة الشعر',
        'price' => 500,
        'duration' => 90,
        'is_featured' => false,
    ],
    
    // خدمات الاستشارات
    [
        'name' => 'استشارة جمالية شاملة',
        'description' => 'استشارة مع خبير تجميل لوضع روتين عناية مخصص',
        'category' => 'استشارات',
        'price' => 150,
        'duration' => 45,
        'is_featured' => false,
    ],
];

foreach ($beautyServices as $serviceData) {
    try {
        \Botble\TrainingAppointment\Models\Service::create([
            'name' => $serviceData['name'],
            'description' => $serviceData['description'],
            'category' => $serviceData['category'],
            'price' => $serviceData['price'],
            'duration' => $serviceData['duration'],
            'status' => 'published',
            'is_featured' => $serviceData['is_featured'],
            'store_id' => $store->id,
        ]);
        
        echo "  ✅ تم إنشاء الخدمة: {$serviceData['name']} ({$serviceData['price']} ريال - {$serviceData['duration']} دقيقة)\n";
        
    } catch (Exception $e) {
        echo "  ❌ خطأ في إنشاء الخدمة {$serviceData['name']}: " . $e->getMessage() . "\n";
    }
}

echo "\n✅ تم إنشاء جميع خدمات العناية والجمال بنجاح!\n";
echo "\n📊 إحصائيات:\n";
echo "  - المتجر: {$store->name}\n";
echo "  - إجمالي الخدمات: " . \Botble\TrainingAppointment\Models\Service::where('store_id', $store->id)->count() . "\n";
echo "  - الخدمات المميزة: " . \Botble\TrainingAppointment\Models\Service::where('store_id', $store->id)->where('is_featured', true)->count() . "\n";

// إحصائيات حسب الفئة
$categories = \Botble\TrainingAppointment\Models\Service::where('store_id', $store->id)
    ->select('category')
    ->groupBy('category')
    ->get()
    ->pluck('category');

echo "  - الفئات المتاحة:\n";
foreach ($categories as $category) {
    $count = \Botble\TrainingAppointment\Models\Service::where('store_id', $store->id)->where('category', $category)->count();
    echo "    • {$category}: {$count} خدمة\n";
}

echo "\n🎉 تم الانتهاء من إعداد خدمات العناية والجمال للفيندور!\n\n";

echo "🌟 ملخص الخدمات المضافة:\n";
echo "✅ خدمات المكياج (3 خدمات): يومي، سهرة، عروس\n";
echo "✅ خدمات العناية بالبشرة (4 خدمات): تنظيف، مضاد شيخوخة، ماسك ذهب، علاج حب الشباب\n";
echo "✅ خدمات العناية بالشعر (4 خدمات): قص، صبغة، كيراتين، حمام كريم\n";
echo "✅ خدمات العناية بالأظافر (4 خدمات): مانيكير، بديكير، نيل آرت، أظافر جل\n";
echo "✅ خدمات إزالة الشعر (2 خدمة): وجه، جسم\n";
echo "✅ خدمات الاستشارات (1 خدمة): استشارة شاملة\n\n";

echo "🚀 الآن يمكن للفيندور إدارة هذه الخدمات من لوحة التحكم!\n";
