<?php

// تحميل Composer autoloader
require_once 'vendor/autoload.php';

// تحميل Laravel application
$app = require_once 'bootstrap/app.php';

// Bootstrap the application
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 تحليل النظام لتطبيق Google Maps Geocoding\n";
echo "=" . str_repeat("=", 60) . "\n\n";

try {
    // 1. فحص جدول mp_stores
    echo "1. 📊 فحص جدول المتاجر (mp_stores):\n";
    
    $storeColumns = \Illuminate\Support\Facades\Schema::getColumnListing('mp_stores');
    echo "  📋 الأعمدة الموجودة:\n";
    foreach ($storeColumns as $column) {
        echo "    - {$column}\n";
    }
    
    // فحص إذا كانت أعمدة الإحداثيات موجودة
    $hasLatitude = in_array('latitude', $storeColumns);
    $hasLongitude = in_array('longitude', $storeColumns);
    
    echo "\n  🗺️ حقول الموقع الجغرافي:\n";
    echo "    - latitude: " . ($hasLatitude ? "✅ موجود" : "❌ مفقود") . "\n";
    echo "    - longitude: " . ($hasLongitude ? "✅ موجود" : "❌ مفقود") . "\n";
    echo "    - address: " . (in_array('address', $storeColumns) ? "✅ موجود" : "❌ مفقود") . "\n";
    echo "    - city: " . (in_array('city', $storeColumns) ? "✅ موجود" : "❌ مفقود") . "\n";
    echo "    - state: " . (in_array('state', $storeColumns) ? "✅ موجود" : "❌ مفقود") . "\n";
    echo "    - country: " . (in_array('country', $storeColumns) ? "✅ موجود" : "❌ مفقود") . "\n";
    
    echo "\n";
    
    // 2. فحص جدول store_locations
    echo "2. 📍 فحص جدول مواقع المتاجر (store_locations):\n";
    
    if (\Illuminate\Support\Facades\Schema::hasTable('store_locations')) {
        echo "  ✅ الجدول موجود\n";
        
        $locationColumns = \Illuminate\Support\Facades\Schema::getColumnListing('store_locations');
        echo "  📋 الأعمدة:\n";
        foreach ($locationColumns as $column) {
            echo "    - {$column}\n";
        }
        
        // عدد المواقع المسجلة
        $locationsCount = \Botble\TrainingAppointment\Models\StoreLocation::count();
        echo "  📊 عدد المواقع المسجلة: {$locationsCount}\n";
        
    } else {
        echo "  ❌ الجدول غير موجود\n";
    }
    
    echo "\n";
    
    // 3. فحص نماذج التسجيل
    echo "3. 📝 فحص نماذج التسجيل:\n";
    
    // فحص BecomeVendorForm
    echo "  🏪 نموذج تسجيل البائع (BecomeVendorForm):\n";
    $vendorFormFields = [
        'shop_name' => 'اسم المتجر',
        'shop_url' => 'رابط المتجر', 
        'shop_phone' => 'هاتف المتجر',
        'address' => 'العنوان',
        'city' => 'المدينة',
        'state' => 'المحافظة',
        'country' => 'البلد'
    ];
    
    foreach ($vendorFormFields as $field => $description) {
        // فحص إذا كان الحقل موجود في النموذج (تقريبي)
        $exists = in_array($field, ['shop_name', 'shop_url', 'shop_phone']); // الحقول الموجودة حالياً
        echo "    - {$description} ({$field}): " . ($exists ? "✅ موجود" : "❌ مفقود") . "\n";
    }
    
    echo "\n";
    
    // 4. فحص البحث الحالي
    echo "4. 🔍 فحص نظام البحث الحالي:\n";
    
    // فحص UniversalSearchService
    if (class_exists('\Botble\TrainingAppointment\Services\UniversalSearchService')) {
        echo "  ✅ UniversalSearchService موجود\n";
        
        // فحص إذا كان يدعم البحث الجغرافي
        $searchServiceFile = 'platform/plugins/training-appointment/src/Services/UniversalSearchService.php';
        if (file_exists($searchServiceFile)) {
            $content = file_get_contents($searchServiceFile);
            $hasLocationSearch = strpos($content, 'latitude') !== false || strpos($content, 'longitude') !== false;
            echo "  🗺️ دعم البحث الجغرافي: " . ($hasLocationSearch ? "✅ موجود" : "❌ مفقود") . "\n";
        }
    } else {
        echo "  ❌ UniversalSearchService غير موجود\n";
    }
    
    echo "\n";
    
    // 5. فحص العملاء والبحث عن الخدمات
    echo "5. 👥 فحص نظام العملاء والبحث:\n";
    
    // فحص إذا كان هناك نظام لحفظ موقع العميل
    $customerColumns = \Illuminate\Support\Facades\Schema::getColumnListing('ec_customers');
    echo "  📋 حقول العملاء المتعلقة بالموقع:\n";
    
    $locationFields = ['address', 'city', 'state', 'country', 'latitude', 'longitude'];
    foreach ($locationFields as $field) {
        $exists = in_array($field, $customerColumns);
        echo "    - {$field}: " . ($exists ? "✅ موجود" : "❌ مفقود") . "\n";
    }
    
    echo "\n";
    
    // 6. فحص الخدمات والدورات
    echo "6. 🎓 فحص الخدمات والدورات:\n";
    
    // فحص جدول الخدمات
    if (\Illuminate\Support\Facades\Schema::hasTable('services')) {
        $serviceColumns = \Illuminate\Support\Facades\Schema::getColumnListing('services');
        echo "  📋 جدول الخدمات:\n";
        echo "    - store_id: " . (in_array('store_id', $serviceColumns) ? "✅ موجود" : "❌ مفقود") . "\n";
        echo "    - location: " . (in_array('location', $serviceColumns) ? "✅ موجود" : "❌ مفقود") . "\n";
    }
    
    // فحص جدول الدورات
    if (\Illuminate\Support\Facades\Schema::hasTable('courses')) {
        $courseColumns = \Illuminate\Support\Facades\Schema::getColumnListing('courses');
        echo "  📋 جدول الدورات:\n";
        echo "    - store_id: " . (in_array('store_id', $courseColumns) ? "✅ موجود" : "❌ مفقود") . "\n";
        echo "    - location: " . (in_array('location', $courseColumns) ? "✅ موجود" : "❌ مفقود") . "\n";
    }
    
    echo "\n";
    
    // 7. تحليل الاحتياجات
    echo "7. 📋 تحليل الاحتياجات للتطوير:\n";
    
    echo "  🎯 المطلوب لتطبيق Google Maps Geocoding:\n\n";
    
    echo "  أ. للبائعين (Vendors):\n";
    if (!$hasLatitude || !$hasLongitude) {
        echo "    ❌ إضافة حقول latitude و longitude لجدول mp_stores\n";
    } else {
        echo "    ✅ حقول الإحداثيات موجودة في جدول mp_stores\n";
    }
    echo "    ❌ تطبيق Google Maps في نموذج تسجيل البائع\n";
    echo "    ❌ تطبيق Google Maps في نموذج تعديل معلومات المتجر\n";
    
    echo "\n  ب. للعملاء (Customers):\n";
    $customerHasLocation = in_array('latitude', $customerColumns) && in_array('longitude', $customerColumns);
    if (!$customerHasLocation) {
        echo "    ❌ إضافة حقول الموقع لجدول ec_customers\n";
    } else {
        echo "    ✅ حقول الموقع موجودة في جدول ec_customers\n";
    }
    echo "    ❌ تطبيق Google Maps في نموذج تسجيل العميل\n";
    echo "    ❌ نظام تحديد الموقع التلقائي\n";
    
    echo "\n  ج. للبحث الجغرافي:\n";
    echo "    ❌ تطوير البحث حسب المسافة\n";
    echo "    ❌ عرض النتائج الأقرب للعميل\n";
    echo "    ❌ فلترة الخدمات حسب نطاق الخدمة\n";
    echo "    ❌ خريطة تفاعلية لعرض النتائج\n";
    
    echo "\n";
    
    // 8. خطة التنفيذ المقترحة
    echo "8. 🚀 خطة التنفيذ المقترحة:\n";
    
    echo "  المرحلة الأولى - البنية التحتية:\n";
    echo "    1. إضافة حقول الإحداثيات للجداول المطلوبة\n";
    echo "    2. تحديث النماذج (Models) لدعم الحقول الجديدة\n";
    echo "    3. إنشاء Migration للتحديثات\n";
    
    echo "\n  المرحلة الثانية - تطبيق Google Maps:\n";
    echo "    1. تطبيق الإضافة في نماذج تسجيل البائعين\n";
    echo "    2. تطبيق الإضافة في نماذج تسجيل العملاء\n";
    echo "    3. إضافة أزرار 'تحديد الموقع' في النماذج\n";
    
    echo "\n  المرحلة الثالثة - البحث الجغرافي:\n";
    echo "    1. تطوير خدمة البحث حسب المسافة\n";
    echo "    2. تحديث واجهة البحث لدعم الفلترة الجغرافية\n";
    echo "    3. إضافة خريطة تفاعلية لعرض النتائج\n";
    
    echo "\n  المرحلة الرابعة - التحسينات:\n";
    echo "    1. نظام تحديد الموقع التلقائي\n";
    echo "    2. حفظ المواقع المفضلة للعملاء\n";
    echo "    3. إشعارات الخدمات القريبة\n";
    
    echo "\n";
    echo "✅ تم الانتهاء من التحليل\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في التحليل: " . $e->getMessage() . "\n";
}
