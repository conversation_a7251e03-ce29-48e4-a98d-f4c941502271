<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Bo<PERSON>ble\ACL\Models\Permission;
use Bo<PERSON>ble\ACL\Models\Role;
use Illuminate\Support\Facades\Auth;

class CheckTrainingPermissions extends Command
{
    protected $signature = 'check:training-permissions';
    protected $description = 'Check training appointment permissions';

    public function handle()
    {
        $this->info('Checking training-appointment permissions...');

        // Check if permissions exist in database
        $permissions = Permission::where('name', 'like', '%training%')
            ->orWhere('name', 'like', '%course%')
            ->orWhere('name', 'like', '%appointment%')
            ->get(['name', 'slug']);

        if ($permissions->isEmpty()) {
            $this->error('No training-appointment permissions found in database!');
            $this->info('Running permission seeder...');
            
            $this->call('db:seed', ['--class' => 'SettingSeeder']);
            
        } else {
            $this->info('Found permissions:');
            foreach ($permissions as $permission) {
                $this->line("- {$permission->name} (slug: {$permission->slug})");
            }
        }

        // Check super admin role
        $superAdmin = Role::where('slug', 'super-admin')->first();
        if ($superAdmin) {
            $this->info('Super admin role found with ID: ' . $superAdmin->id);
            
            // Get super admin permissions
            $adminPermissions = $superAdmin->permissions()
                ->where('name', 'like', '%training%')
                ->orWhere('name', 'like', '%course%')
                ->orWhere('name', 'like', '%appointment%')
                ->get(['name']);
                
            if ($adminPermissions->isEmpty()) {
                $this->warn('Super admin does not have training permissions!');
            } else {
                $this->info('Super admin has these training permissions:');
                foreach ($adminPermissions as $perm) {
                    $this->line("- {$perm->name}");
                }
            }
        }

        return 0;
    }
}