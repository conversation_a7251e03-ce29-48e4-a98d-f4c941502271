<?php

if (!function_exists('getLocalizedText')) {
    /**
     * استخراج النص المحلي من الحقول متعددة اللغات
     *
     * @param mixed $value القيمة المراد استخراجها
     * @param string $locale اللغة المطلوبة (افتراضي: ar)
     * @return string النص المحلي
     */
    function getLocalizedText($value, $locale = 'ar')
    {
        if (is_null($value)) {
            return '';
        }

        if (is_array($value)) {
            // إذا كانت القيمة مصفوفة، نحاول استخراج النص باللغة المطلوبة
            if (isset($value[$locale]) && !empty($value[$locale])) {
                return (string) $value[$locale];
            }

            // إذا لم نجد اللغة المطلوبة، نبحث عن أول قيمة متاحة
            foreach ($value as $key => $val) {
                if (!empty($val)) {
                    return (string) $val;
                }
            }

            return '';
        }

        if (is_string($value)) {
            // إذا كانت القيمة نص JSON، نحاول فك التشفير
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                return getLocalizedText($decoded, $locale);
            }
        }

        // إذا كانت القيمة عادية، نرجعها كما هي
        return (string) $value;
    }
}

if (!function_exists('safeOutput')) {
    /**
     * إخراج آمن للنصوص متعددة اللغات
     *
     * @param mixed $value القيمة المراد إخراجها
     * @param string $locale اللغة المطلوبة
     * @param int $limit حد الحروف (اختياري)
     * @return string النص الآمن للإخراج
     */
    function safeOutput($value, $locale = 'ar', $limit = null)
    {
        // التعامل مع القيم الفارغة
        if (is_null($value)) {
            return '';
        }

        // التعامل مع المصفوفات بشكل آمن
        if (is_array($value)) {
            // إذا كانت مصفوفة بسيطة مع مفتاح للغة المطلوبة
            if (isset($value[$locale]) && is_string($value[$locale])) {
                $value = $value[$locale];
            }
            // إذا كانت مصفوفة بدون مفتاح للغة المطلوبة، نحاول استخدام أول قيمة نصية
            elseif (count($value) > 0) {
                foreach ($value as $val) {
                    if (is_string($val)) {
                        $value = $val;
                        break;
                    }
                }
                // إذا لم نجد قيمة نصية، نرجع نص فارغ
                if (is_array($value)) {
                    return '';
                }
            } else {
                return '';
            }
        }

        $text = getLocalizedText($value, $locale);

        // تأكد من أن النص هو سلسلة نصية - إصلاح مهم لمنع خطأ htmlspecialchars
        if (is_array($text)) {
            // إذا كان النص لا يزال مصفوفة، نحاول استخراج قيمة نصية منه
            if (isset($text[$locale])) {
                $text = (string) $text[$locale];
            } elseif (!empty($text)) {
                $text = (string) reset($text); // أول قيمة في المصفوفة
            } else {
                $text = '';
            }
        } elseif (!is_string($text)) {
            $text = (string) $text;
        }

        // التأكد النهائي من أن النص هو سلسلة نصية
        if (!is_string($text)) {
            $text = '';
        }

        if ($limit && strlen($text) > $limit) {
            $text = mb_substr($text, 0, $limit) . '...';
        }

        // تطبيق htmlspecialchars على النص قبل إرجاعه لضمان أمان الإخراج
        // تحقق إضافي للتأكد من أن $text هو نص وليس مصفوفة
        // إضافة تحويل صريح إلى نص لمنع خطأ htmlspecialchars
        return htmlspecialchars((string) $text, ENT_QUOTES, 'UTF-8', false);
    }
}
