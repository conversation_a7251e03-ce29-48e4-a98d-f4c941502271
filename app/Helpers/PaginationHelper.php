<?php

namespace App\Helpers;

use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\HtmlString;

if (!function_exists('safe_pagination_links')) {
    /**
     * إنشاء روابط ترقيم صفحي آمنة للاستخدام في قوالب Blade
     *
     * @param LengthAwarePaginator $paginator كائن الترقيم الصفحي
     * @param string|null $view اسم القالب المستخدم (اختياري)
     * @param array $data بيانات إضافية للقالب (اختياري)
     * @return string النص الآمن لروابط الترقيم الصفحي
     */
    function safe_pagination_links($paginator, $view = null, $data = [])
    {
        if (!$paginator instanceof LengthAwarePaginator) {
            return '';
        }

        // الحصول على روابط الترقيم الصفحي
        $links = $paginator->links($view, $data);

        // التحقق من نوع الروابط وتحويلها إلى نص
        if ($links instanceof HtmlString) {
            return $links->toHtml();
        }

        // إذا كانت الروابط مصفوفة، نحاول تحويلها إلى نص
        if (is_array($links)) {
            return json_encode($links);
        }

        // إذا كانت الروابط من نوع آخر، نحاول تحويلها إلى نص
        return (string) $links;
    }
}