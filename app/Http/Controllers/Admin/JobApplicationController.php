<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\JobApplicationUpdateRequest;
use App\Http\Requests\Admin\JobRecommendationRequest;
use App\Models\JobApplication;
use App\Models\JobCategory;
use App\Models\JobRecommendation;
use App\Notifications\JobApplicationStatusUpdated;
use App\Notifications\JobRecommendationReceived;
use Botble\Marketplace\Models\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;

class JobApplicationController extends Controller
{
    /**
     * عرض قائمة طلبات التوظيف
     */
    public function index(Request $request)
    {
        $query = JobApplication::with(['customer', 'preferredJobCategory', 'recommendations'])
            ->orderBy('created_at', 'desc');

        // تصفية حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // تصفية حسب الفئة
        if ($request->filled('category')) {
            $query->where('preferred_job_category_id', $request->category);
        }

        // تصفية حسب المدينة
        if ($request->filled('city')) {
            $query->where('city', 'like', '%' . $request->city . '%');
        }

        // البحث في الاسم أو البريد الإلكتروني
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('full_name', 'like', '%' . $search . '%')
                  ->orWhere('email', 'like', '%' . $search . '%')
                  ->orWhere('phone', 'like', '%' . $search . '%');
            });
        }

        $jobApplications = $query->paginate(20);
        $jobCategories = JobCategory::active()->ordered()->get();
        $statuses = JobApplication::getStatuses();

        return view('admin.job-applications.index', compact(
            'jobApplications', 'jobCategories', 'statuses'
        ));
    }

    /**
     * عرض تفاصيل طلب التوظيف
     */
    public function show(JobApplication $jobApplication)
    {
        $jobApplication->load([
            'customer', 
            'preferredJobCategory', 
            'skills', 
            'recommendations.store',
            'recommendations.admin'
        ]);

        return view('admin.job-applications.show', compact('jobApplication'));
    }

    /**
     * تحديث حالة طلب التوظيف
     */
    public function update(JobApplicationUpdateRequest $request, JobApplication $jobApplication)
    {
        try {
            $oldStatus = $jobApplication->status;
            $jobApplication->update($request->validated());

            // إرسال إشعار للعميل بتحديث الحالة
            if ($oldStatus !== $jobApplication->status) {
                $this->notifyCustomerOfStatusUpdate($jobApplication, $oldStatus, $jobApplication->status);
            }

            return redirect()->route('admin.job-applications.show', $jobApplication)
                ->with('success', 'تم تحديث حالة الطلب بنجاح.');

        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء تحديث الطلب.');
        }
    }

    /**
     * عرض نموذج ترشيح المرشح للفيندورز
     */
    public function showRecommendForm(JobApplication $jobApplication)
    {
        if ($jobApplication->status !== 'approved') {
            return redirect()->route('admin.job-applications.show', $jobApplication)
                ->with('error', 'يجب الموافقة على الطلب أولاً قبل الترشيح.');
        }

        // الحصول على المتاجر المناسبة حسب فئة الوظيفة
        $stores = Store::whereHas('categories', function($query) use ($jobApplication) {
            if ($jobApplication->preferred_job_category_id) {
                // يمكن إضافة منطق ربط فئات الوظائف بفئات المتاجر
                $query->where('name', 'like', '%' . $jobApplication->preferredJobCategory->name . '%');
            }
        })->with('customer')->get();

        // إذا لم نجد متاجر مناسبة، نعرض جميع المتاجر
        if ($stores->isEmpty()) {
            $stores = Store::with('customer')->get();
        }

        return view('admin.job-applications.recommend', compact('jobApplication', 'stores'));
    }

    /**
     * ترشيح المرشح للفيندورز
     */
    public function recommend(JobRecommendationRequest $request)
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            $jobApplication = JobApplication::findOrFail($data['job_application_id']);

            foreach ($data['store_ids'] as $storeId) {
                // التحقق من عدم وجود ترشيح سابق لنفس المتجر
                $existingRecommendation = JobRecommendation::where([
                    'job_application_id' => $jobApplication->id,
                    'store_id' => $storeId
                ])->first();

                if (!$existingRecommendation) {
                    JobRecommendation::create([
                        'job_application_id' => $jobApplication->id,
                        'store_id' => $storeId,
                        'admin_id' => auth()->id(),
                        'admin_notes' => $data['admin_notes'],
                        'recommended_at' => now(),
                    ]);
                }
            }

            // تحديث حالة طلب التوظيف
            $jobApplication->update(['status' => 'recommended']);

            DB::commit();

            // إرسال إشعارات للفيندورز
            $this->notifyVendorsOfRecommendation($jobApplication, $data['store_ids']);

            return redirect()->route('admin.job-applications.show', $jobApplication)
                ->with('success', 'تم ترشيح المرشح للفيندورز بنجاح.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'حدث خطأ أثناء الترشيح.');
        }
    }

    /**
     * حذف طلب التوظيف
     */
    public function destroy(JobApplication $jobApplication)
    {
        try {
            // حذف ملف السيرة الذاتية
            if ($jobApplication->cv_file) {
                \Storage::disk('public')->delete($jobApplication->cv_file);
            }

            $jobApplication->delete();

            return redirect()->route('admin.job-applications.index')
                ->with('success', 'تم حذف طلب التوظيف بنجاح.');

        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء حذف الطلب.');
        }
    }

    /**
     * إحصائيات طلبات التوظيف
     */
    public function statistics()
    {
        $stats = [
            'total' => JobApplication::count(),
            'pending' => JobApplication::where('status', 'pending')->count(),
            'under_review' => JobApplication::where('status', 'under_review')->count(),
            'approved' => JobApplication::where('status', 'approved')->count(),
            'rejected' => JobApplication::where('status', 'rejected')->count(),
            'recommended' => JobApplication::where('status', 'recommended')->count(),
        ];

        $categoryStats = JobApplication::join('job_categories', 'job_applications.preferred_job_category_id', '=', 'job_categories.id')
            ->selectRaw('job_categories.name, COUNT(*) as count')
            ->groupBy('job_categories.id', 'job_categories.name')
            ->get();

        $monthlyStats = JobApplication::selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return view('admin.job-applications.statistics', compact('stats', 'categoryStats', 'monthlyStats'));
    }

    /**
     * إرسال إشعار للعميل عند تحديث حالة الطلب
     */
    private function notifyCustomerOfStatusUpdate(JobApplication $jobApplication, string $oldStatus, string $newStatus): void
    {
        try {
            if ($jobApplication->customer) {
                $jobApplication->customer->notify(new JobApplicationStatusUpdated($jobApplication, $oldStatus, $newStatus));
            }
        } catch (\Exception $e) {
            \Log::error('فشل في إرسال إشعار تحديث حالة طلب التوظيف: ' . $e->getMessage());
        }
    }

    /**
     * إرسال إشعارات للفيندورز عند الترشيح
     */
    private function notifyVendorsOfRecommendation(JobApplication $jobApplication, array $storeIds): void
    {
        try {
            foreach ($storeIds as $storeId) {
                $store = Store::find($storeId);
                if ($store && $store->customer) {
                    $recommendation = JobRecommendation::where([
                        'job_application_id' => $jobApplication->id,
                        'store_id' => $storeId
                    ])->first();

                    if ($recommendation) {
                        $store->customer->notify(new JobRecommendationReceived($recommendation));
                    }
                }
            }
        } catch (\Exception $e) {
            \Log::error('فشل في إرسال إشعارات الترشيح للفيندورز: ' . $e->getMessage());
        }
    }
}
