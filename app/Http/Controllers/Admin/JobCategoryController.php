<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\JobCategory;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class JobCategoryController extends Controller
{
    /**
     * عرض قائمة فئات الوظائف
     */
    public function index(Request $request): View
    {
        $query = JobCategory::query();

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                  ->orWhere('description', 'like', '%' . $search . '%');
            });
        }

        // تصفية حسب الحالة
        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->where('is_active', $isActive);
        }

        $categories = $query->orderBy('sort_order')
                           ->orderBy('name')
                           ->paginate(20);

        // إحصائيات سريعة
        $stats = [
            'total' => JobCategory::count(),
            'active' => JobCategory::where('is_active', true)->count(),
            'inactive' => JobCategory::where('is_active', false)->count(),
        ];

        return view('admin.job-categories.index', compact('categories', 'stats'));
    }

    /**
     * عرض نموذج إنشاء فئة جديدة
     */
    public function create(): View
    {
        return view('admin.job-categories.create');
    }

    /**
     * حفظ فئة جديدة
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:job_categories,name',
            'description' => 'nullable|string|max:1000',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        JobCategory::create([
            'name' => $request->name,
            'description' => $request->description,
            'is_active' => $request->status === 'active',
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->route('job-categories.index')
                        ->with('success', 'تم إنشاء فئة الوظيفة بنجاح');
    }

    /**
     * عرض تفاصيل فئة الوظيفة
     */
    public function show(JobCategory $jobCategory): View
    {
        // عدد طلبات التوظيف في هذه الفئة
        $applicationsCount = $jobCategory->jobApplications()->count();
        
        return view('admin.job-categories.show', compact('jobCategory', 'applicationsCount'));
    }

    /**
     * عرض نموذج تعديل فئة الوظيفة
     */
    public function edit(JobCategory $jobCategory): View
    {
        return view('admin.job-categories.edit', compact('jobCategory'));
    }

    /**
     * تحديث فئة الوظيفة
     */
    public function update(Request $request, JobCategory $jobCategory): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:job_categories,name,' . $jobCategory->id,
            'description' => 'nullable|string|max:1000',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $jobCategory->update([
            'name' => $request->name,
            'description' => $request->description,
            'is_active' => $request->status === 'active',
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->route('job-categories.index')
                        ->with('success', 'تم تحديث فئة الوظيفة بنجاح');
    }

    /**
     * حذف فئة الوظيفة
     */
    public function destroy(JobCategory $jobCategory): RedirectResponse
    {
        // التحقق من وجود طلبات توظيف مرتبطة
        $applicationsCount = $jobCategory->jobApplications()->count();
        
        if ($applicationsCount > 0) {
            return redirect()->route('job-categories.index')
                            ->with('error', "لا يمكن حذف هذه الفئة لأنها مرتبطة بـ {$applicationsCount} طلب توظيف");
        }

        $jobCategory->delete();

        return redirect()->route('job-categories.index')
                        ->with('success', 'تم حذف فئة الوظيفة بنجاح');
    }

    /**
     * تغيير حالة فئة الوظيفة
     */
    public function toggleStatus(JobCategory $jobCategory): RedirectResponse
    {
        $newStatus = !$jobCategory->is_active;
        $jobCategory->update(['is_active' => $newStatus]);

        $statusText = $newStatus ? 'تفعيل' : 'إلغاء تفعيل';

        return redirect()->route('job-categories.index')
                        ->with('success', "تم {$statusText} فئة الوظيفة بنجاح");
    }

    /**
     * حذف متعدد
     */
    public function bulkDelete(Request $request): RedirectResponse
    {
        $request->validate([
            'categories' => 'required|array',
            'categories.*' => 'exists:job_categories,id',
        ]);

        $categories = JobCategory::whereIn('id', $request->categories)->get();
        $deletedCount = 0;
        $errors = [];

        foreach ($categories as $category) {
            $applicationsCount = $category->jobApplications()->count();
            
            if ($applicationsCount > 0) {
                $errors[] = "لا يمكن حذف فئة '{$category->name}' لأنها مرتبطة بـ {$applicationsCount} طلب توظيف";
            } else {
                $category->delete();
                $deletedCount++;
            }
        }

        if ($deletedCount > 0) {
            $message = "تم حذف {$deletedCount} فئة بنجاح";
            if (!empty($errors)) {
                $message .= ". " . implode('. ', $errors);
            }
            return redirect()->route('job-categories.index')->with('success', $message);
        } else {
            return redirect()->route('job-categories.index')->with('error', implode('. ', $errors));
        }
    }
}
