<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\JobSkill;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class JobSkillController extends Controller
{
    /**
     * عرض قائمة مهارات الوظائف
     */
    public function index(Request $request): View
    {
        $query = JobSkill::query();

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                  ->orWhere('description', 'like', '%' . $search . '%');
            });
        }

        // تصفية حسب النوع
        if ($request->filled('type')) {
            $query->where('category', $request->type);
        }

        $skills = $query->orderBy('name')
                       ->paginate(20);

        // إحصائيات سريعة
        $stats = [
            'total' => JobSkill::count(),
            'technical' => JobSkill::where('category', 'technical')->count(),
            'soft' => JobSkill::where('category', 'soft')->count(),
            'language' => JobSkill::where('category', 'language')->count(),
        ];

        return view('admin.job-skills.index', compact('skills', 'stats'));
    }

    /**
     * عرض نموذج إنشاء مهارة جديدة
     */
    public function create(): View
    {
        return view('admin.job-skills.create');
    }

    /**
     * حفظ مهارة جديدة
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:job_skills,name',
            'description' => 'nullable|string|max:1000',
            'category' => 'required|in:technical,soft,language,other',
        ]);

        JobSkill::create([
            'name' => $request->name,
            'description' => $request->description,
            'category' => $request->category,
        ]);

        return redirect()->route('job-skills.index')
                        ->with('success', 'تم إنشاء المهارة بنجاح');
    }

    /**
     * عرض تفاصيل المهارة
     */
    public function show(JobSkill $jobSkill): View
    {
        // عدد طلبات التوظيف التي تحتوي على هذه المهارة
        $applicationsCount = $jobSkill->jobApplications()->count();
        
        return view('admin.job-skills.show', compact('jobSkill', 'applicationsCount'));
    }

    /**
     * عرض نموذج تعديل المهارة
     */
    public function edit(JobSkill $jobSkill): View
    {
        return view('admin.job-skills.edit', compact('jobSkill'));
    }

    /**
     * تحديث المهارة
     */
    public function update(Request $request, JobSkill $jobSkill): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:job_skills,name,' . $jobSkill->id,
            'description' => 'nullable|string|max:1000',
            'category' => 'required|in:technical,soft,language,other',
        ]);

        $jobSkill->update([
            'name' => $request->name,
            'description' => $request->description,
            'category' => $request->category,
        ]);

        return redirect()->route('job-skills.index')
                        ->with('success', 'تم تحديث المهارة بنجاح');
    }

    /**
     * حذف المهارة
     */
    public function destroy(JobSkill $jobSkill): RedirectResponse
    {
        // التحقق من وجود طلبات توظيف مرتبطة
        $applicationsCount = $jobSkill->jobApplications()->count();
        
        if ($applicationsCount > 0) {
            return redirect()->route('job-skills.index')
                            ->with('error', "لا يمكن حذف هذه المهارة لأنها مرتبطة بـ {$applicationsCount} طلب توظيف");
        }

        $jobSkill->delete();

        return redirect()->route('job-skills.index')
                        ->with('success', 'تم حذف المهارة بنجاح');
    }

    /**
     * حذف متعدد
     */
    public function bulkDelete(Request $request): RedirectResponse
    {
        $request->validate([
            'skills' => 'required|array',
            'skills.*' => 'exists:job_skills,id',
        ]);

        $skills = JobSkill::whereIn('id', $request->skills)->get();
        $deletedCount = 0;
        $errors = [];

        foreach ($skills as $skill) {
            $applicationsCount = $skill->jobApplications()->count();
            
            if ($applicationsCount > 0) {
                $errors[] = "لا يمكن حذف مهارة '{$skill->name}' لأنها مرتبطة بـ {$applicationsCount} طلب توظيف";
            } else {
                $skill->delete();
                $deletedCount++;
            }
        }

        if ($deletedCount > 0) {
            $message = "تم حذف {$deletedCount} مهارة بنجاح";
            if (!empty($errors)) {
                $message .= ". " . implode('. ', $errors);
            }
            return redirect()->route('job-skills.index')->with('success', $message);
        } else {
            return redirect()->route('job-skills.index')->with('error', implode('. ', $errors));
        }
    }
}
