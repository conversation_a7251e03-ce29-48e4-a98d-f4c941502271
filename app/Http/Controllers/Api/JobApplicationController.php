<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\JobApplicationResource;
use App\Models\JobApplication;
use App\Models\JobCategory;
use App\Models\JobSkill;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class JobApplicationController extends Controller
{
    /**
     * عرض قائمة طلبات التوظيف
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = JobApplication::with(['category', 'skills']);

            // فلترة حسب الفئة
            if ($request->filled('category_id')) {
                $query->where('job_category_id', $request->category_id);
            }

            // فلترة حسب الحالة
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // البحث
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('full_name', 'like', '%' . $search . '%')
                      ->orWhere('email', 'like', '%' . $search . '%')
                      ->orWhere('phone', 'like', '%' . $search . '%');
                });
            }

            // ترتيب النتائج
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // تحديد عدد النتائج في الصفحة
            $perPage = $request->get('per_page', 15);
            $applications = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'تم جلب طلبات التوظيف بنجاح',
                'data' => JobApplicationResource::collection($applications->items()),
                'pagination' => [
                    'current_page' => $applications->currentPage(),
                    'last_page' => $applications->lastPage(),
                    'per_page' => $applications->perPage(),
                    'total' => $applications->total(),
                    'from' => $applications->firstItem(),
                    'to' => $applications->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب طلبات التوظيف',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * إنشاء طلب توظيف جديد
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'full_name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'required|string|max:20',
                'date_of_birth' => 'required|date',
                'gender' => 'required|in:male,female',
                'nationality' => 'required|string|max:100',
                'city' => 'required|string|max:100',
                'address' => 'required|string|max:500',
                'job_category_id' => 'required|exists:job_categories,id',
                'education_level' => 'required|string|max:100',
                'years_of_experience' => 'required|integer|min:0',
                'previous_experience' => 'nullable|string|max:2000',
                'skills' => 'required|array|min:1',
                'skills.*.skill_id' => 'required|exists:job_skills,id',
                'skills.*.proficiency_level' => 'required|in:beginner,intermediate,advanced,expert',
                'skills.*.years_experience' => 'required|integer|min:0',
                'cv_file' => 'nullable|file|mimes:pdf,doc,docx|max:5120',
                'cover_letter' => 'nullable|string|max:2000',
                'availability_date' => 'required|date|after_or_equal:today',
                'expected_salary' => 'nullable|numeric|min:0',
                'work_type_preference' => 'required|in:full_time,part_time,contract,remote',
                'additional_notes' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            // رفع ملف السيرة الذاتية
            $cvPath = null;
            if ($request->hasFile('cv_file')) {
                $cvPath = $request->file('cv_file')->store('job-applications/cvs', 'public');
            }

            // إنشاء طلب التوظيف
            $application = JobApplication::create([
                'full_name' => $request->full_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'date_of_birth' => $request->date_of_birth,
                'gender' => $request->gender,
                'nationality' => $request->nationality,
                'city' => $request->city,
                'address' => $request->address,
                'job_category_id' => $request->job_category_id,
                'education_level' => $request->education_level,
                'years_of_experience' => $request->years_of_experience,
                'previous_experience' => $request->previous_experience,
                'cv_file_path' => $cvPath,
                'cover_letter' => $request->cover_letter,
                'availability_date' => $request->availability_date,
                'expected_salary' => $request->expected_salary,
                'work_type_preference' => $request->work_type_preference,
                'additional_notes' => $request->additional_notes,
                'status' => 'pending',
                'application_date' => now(),
            ]);

            // ربط المهارات
            foreach ($request->skills as $skill) {
                $application->skills()->attach($skill['skill_id'], [
                    'proficiency_level' => $skill['proficiency_level'],
                    'years_experience' => $skill['years_experience'],
                ]);
            }

            // تحميل العلاقات
            $application->load(['category', 'skills']);

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال طلب التوظيف بنجاح',
                'data' => $application
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال طلب التوظيف',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض تفاصيل طلب توظيف محدد
     */
    public function show($id): JsonResponse
    {
        try {
            $application = JobApplication::with(['category', 'skills'])->find($id);

            if (!$application) {
                return response()->json([
                    'success' => false,
                    'message' => 'طلب التوظيف غير موجود'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم جلب تفاصيل طلب التوظيف بنجاح',
                'data' => $application
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب تفاصيل طلب التوظيف',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحديث طلب توظيف
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $application = JobApplication::find($id);

            if (!$application) {
                return response()->json([
                    'success' => false,
                    'message' => 'طلب التوظيف غير موجود'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'status' => 'sometimes|in:pending,under_review,approved,rejected',
                'admin_notes' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $application->update($request->only(['status', 'admin_notes']));
            $application->load(['category', 'skills']);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث طلب التوظيف بنجاح',
                'data' => $application
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث طلب التوظيف',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * حذف طلب توظيف
     */
    public function destroy($id): JsonResponse
    {
        try {
            $application = JobApplication::find($id);

            if (!$application) {
                return response()->json([
                    'success' => false,
                    'message' => 'طلب التوظيف غير موجود'
                ], 404);
            }

            // حذف ملف السيرة الذاتية
            if ($application->cv_file_path) {
                Storage::disk('public')->delete($application->cv_file_path);
            }

            // حذف العلاقات
            $application->skills()->detach();
            
            // حذف الطلب
            $application->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف طلب التوظيف بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف طلب التوظيف',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحميل ملف السيرة الذاتية
     */
    public function downloadCv($id): JsonResponse
    {
        try {
            $application = JobApplication::find($id);

            if (!$application || !$application->cv_file_path) {
                return response()->json([
                    'success' => false,
                    'message' => 'ملف السيرة الذاتية غير موجود'
                ], 404);
            }

            $filePath = storage_path('app/public/' . $application->cv_file_path);
            
            if (!file_exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'ملف السيرة الذاتية غير موجود'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'رابط تحميل السيرة الذاتية',
                'data' => [
                    'download_url' => asset('storage/' . $application->cv_file_path),
                    'file_name' => basename($application->cv_file_path),
                    'file_size' => filesize($filePath)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحميل السيرة الذاتية',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
