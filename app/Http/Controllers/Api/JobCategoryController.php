<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\JobCategory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class JobCategoryController extends Controller
{
    /**
     * عرض قائمة فئات الوظائف
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = JobCategory::query();

            // فلترة الفئات النشطة فقط
            if ($request->get('active_only', true)) {
                $query->where('is_active', true);
            }

            // البحث
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', '%' . $search . '%')
                      ->orWhere('description', 'like', '%' . $search . '%');
                });
            }

            // ترتيب النتائج
            $categories = $query->orderBy('sort_order')
                               ->orderBy('name')
                               ->get();

            // إضافة عدد طلبات التوظيف لكل فئة
            $categories->each(function ($category) {
                $category->applications_count = $category->jobApplications()->count();
            });

            return response()->json([
                'success' => true,
                'message' => 'تم جلب فئات الوظائف بنجاح',
                'data' => $categories
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب فئات الوظائف',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض تفاصيل فئة وظيفة محددة
     */
    public function show($id): JsonResponse
    {
        try {
            $category = JobCategory::find($id);

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'فئة الوظيفة غير موجودة'
                ], 404);
            }

            // إضافة إحصائيات
            $category->applications_count = $category->jobApplications()->count();
            $category->pending_applications = $category->jobApplications()->where('status', 'pending')->count();
            $category->approved_applications = $category->jobApplications()->where('status', 'approved')->count();

            return response()->json([
                'success' => true,
                'message' => 'تم جلب تفاصيل فئة الوظيفة بنجاح',
                'data' => $category
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب تفاصيل فئة الوظيفة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض طلبات التوظيف لفئة محددة
     */
    public function applications($id, Request $request): JsonResponse
    {
        try {
            $category = JobCategory::find($id);

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'فئة الوظيفة غير موجودة'
                ], 404);
            }

            $query = $category->jobApplications()->with(['skills']);

            // فلترة حسب الحالة
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // البحث
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('full_name', 'like', '%' . $search . '%')
                      ->orWhere('email', 'like', '%' . $search . '%')
                      ->orWhere('phone', 'like', '%' . $search . '%');
                });
            }

            // ترتيب النتائج
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // تحديد عدد النتائج في الصفحة
            $perPage = $request->get('per_page', 15);
            $applications = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'تم جلب طلبات التوظيف للفئة بنجاح',
                'data' => [
                    'category' => $category,
                    'applications' => $applications->items(),
                    'pagination' => [
                        'current_page' => $applications->currentPage(),
                        'last_page' => $applications->lastPage(),
                        'per_page' => $applications->perPage(),
                        'total' => $applications->total(),
                        'from' => $applications->firstItem(),
                        'to' => $applications->lastItem(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب طلبات التوظيف للفئة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * إحصائيات فئات الوظائف
     */
    public function statistics(): JsonResponse
    {
        try {
            $totalCategories = JobCategory::count();
            $activeCategories = JobCategory::where('is_active', true)->count();
            $inactiveCategories = JobCategory::where('is_active', false)->count();

            // إحصائيات طلبات التوظيف حسب الفئة
            $categoriesWithStats = JobCategory::withCount([
                'jobApplications',
                'jobApplications as pending_applications_count' => function ($query) {
                    $query->where('status', 'pending');
                },
                'jobApplications as approved_applications_count' => function ($query) {
                    $query->where('status', 'approved');
                },
                'jobApplications as rejected_applications_count' => function ($query) {
                    $query->where('status', 'rejected');
                }
            ])->orderBy('job_applications_count', 'desc')->get();

            return response()->json([
                'success' => true,
                'message' => 'تم جلب إحصائيات فئات الوظائف بنجاح',
                'data' => [
                    'summary' => [
                        'total_categories' => $totalCategories,
                        'active_categories' => $activeCategories,
                        'inactive_categories' => $inactiveCategories,
                    ],
                    'categories_stats' => $categoriesWithStats
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب إحصائيات فئات الوظائف',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
