<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\JobSkill;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class JobSkillController extends Controller
{
    /**
     * عرض قائمة مهارات الوظائف
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = JobSkill::query();

            // فلترة المهارات النشطة فقط
            if ($request->get('active_only', true)) {
                $query->where('is_active', true);
            }

            // فلترة حسب النوع
            if ($request->filled('category')) {
                $query->where('category', $request->category);
            }

            // البحث
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', '%' . $search . '%')
                      ->orWhere('description', 'like', '%' . $search . '%');
                });
            }

            // ترتيب النتائج
            $skills = $query->orderBy('name')->get();

            // تجميع المهارات حسب النوع
            if ($request->get('group_by_category', false)) {
                $groupedSkills = $skills->groupBy('category');
                
                $result = [];
                foreach ($groupedSkills as $category => $categorySkills) {
                    $result[] = [
                        'category' => $category,
                        'category_name' => $this->getCategoryName($category),
                        'skills' => $categorySkills->values()
                    ];
                }

                return response()->json([
                    'success' => true,
                    'message' => 'تم جلب مهارات الوظائف مجمعة حسب النوع بنجاح',
                    'data' => $result
                ]);
            }

            // إضافة عدد طلبات التوظيف لكل مهارة
            $skills->each(function ($skill) {
                $skill->applications_count = $skill->jobApplications()->count();
            });

            return response()->json([
                'success' => true,
                'message' => 'تم جلب مهارات الوظائف بنجاح',
                'data' => $skills
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب مهارات الوظائف',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض تفاصيل مهارة محددة
     */
    public function show($id): JsonResponse
    {
        try {
            $skill = JobSkill::find($id);

            if (!$skill) {
                return response()->json([
                    'success' => false,
                    'message' => 'المهارة غير موجودة'
                ], 404);
            }

            // إضافة إحصائيات
            $skill->applications_count = $skill->jobApplications()->count();
            $skill->category_name = $this->getCategoryName($skill->category);

            // إحصائيات مستويات الإتقان
            $proficiencyStats = $skill->jobApplications()
                ->join('job_application_skills', 'job_applications.id', '=', 'job_application_skills.job_application_id')
                ->where('job_application_skills.job_skill_id', $skill->id)
                ->selectRaw('proficiency_level, COUNT(*) as count')
                ->groupBy('proficiency_level')
                ->pluck('count', 'proficiency_level')
                ->toArray();

            $skill->proficiency_stats = $proficiencyStats;

            return response()->json([
                'success' => true,
                'message' => 'تم جلب تفاصيل المهارة بنجاح',
                'data' => $skill
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب تفاصيل المهارة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض طلبات التوظيف لمهارة محددة
     */
    public function applications($id, Request $request): JsonResponse
    {
        try {
            $skill = JobSkill::find($id);

            if (!$skill) {
                return response()->json([
                    'success' => false,
                    'message' => 'المهارة غير موجودة'
                ], 404);
            }

            $query = $skill->jobApplications()->with(['category']);

            // فلترة حسب الحالة
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // فلترة حسب مستوى الإتقان
            if ($request->filled('proficiency_level')) {
                $query->whereHas('skills', function($q) use ($skill, $request) {
                    $q->where('job_skill_id', $skill->id)
                      ->where('proficiency_level', $request->proficiency_level);
                });
            }

            // البحث
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('full_name', 'like', '%' . $search . '%')
                      ->orWhere('email', 'like', '%' . $search . '%')
                      ->orWhere('phone', 'like', '%' . $search . '%');
                });
            }

            // ترتيب النتائج
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // تحديد عدد النتائج في الصفحة
            $perPage = $request->get('per_page', 15);
            $applications = $query->paginate($perPage);

            // إضافة معلومات المهارة لكل طلب
            $applications->getCollection()->transform(function ($application) use ($skill) {
                $skillPivot = $application->skills()->where('job_skill_id', $skill->id)->first();
                if ($skillPivot) {
                    $application->skill_info = [
                        'proficiency_level' => $skillPivot->pivot->proficiency_level,
                        'years_experience' => $skillPivot->pivot->years_experience,
                    ];
                }
                return $application;
            });

            return response()->json([
                'success' => true,
                'message' => 'تم جلب طلبات التوظيف للمهارة بنجاح',
                'data' => [
                    'skill' => $skill,
                    'applications' => $applications->items(),
                    'pagination' => [
                        'current_page' => $applications->currentPage(),
                        'last_page' => $applications->lastPage(),
                        'per_page' => $applications->perPage(),
                        'total' => $applications->total(),
                        'from' => $applications->firstItem(),
                        'to' => $applications->lastItem(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب طلبات التوظيف للمهارة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * إحصائيات مهارات الوظائف
     */
    public function statistics(): JsonResponse
    {
        try {
            $totalSkills = JobSkill::count();
            $activeSkills = JobSkill::where('is_active', true)->count();
            $inactiveSkills = JobSkill::where('is_active', false)->count();

            // إحصائيات حسب النوع
            $skillsByCategory = JobSkill::selectRaw('category, COUNT(*) as count')
                ->groupBy('category')
                ->pluck('count', 'category')
                ->toArray();

            // أكثر المهارات طلباً
            $topSkills = JobSkill::withCount('jobApplications')
                ->orderBy('job_applications_count', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'تم جلب إحصائيات مهارات الوظائف بنجاح',
                'data' => [
                    'summary' => [
                        'total_skills' => $totalSkills,
                        'active_skills' => $activeSkills,
                        'inactive_skills' => $inactiveSkills,
                    ],
                    'skills_by_category' => $skillsByCategory,
                    'top_skills' => $topSkills
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب إحصائيات مهارات الوظائف',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على اسم الفئة باللغة العربية
     */
    private function getCategoryName($category): string
    {
        $categories = [
            'technical' => 'تقنية',
            'soft' => 'شخصية',
            'language' => 'لغة',
            'other' => 'أخرى'
        ];

        return $categories[$category] ?? $category;
    }
}
