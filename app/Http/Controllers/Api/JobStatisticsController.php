<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\JobApplication;
use App\Models\JobCategory;
use App\Models\JobSkill;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class JobStatisticsController extends Controller
{
    /**
     * إحصائيات عامة شاملة
     */
    public function overview(): JsonResponse
    {
        try {
            // إحصائيات طلبات التوظيف
            $totalApplications = JobApplication::count();
            $pendingApplications = JobApplication::where('status', 'pending')->count();
            $approvedApplications = JobApplication::where('status', 'approved')->count();
            $rejectedApplications = JobApplication::where('status', 'rejected')->count();
            $underReviewApplications = JobApplication::where('status', 'under_review')->count();

            // إحصائيات الفئات والمهارات
            $totalCategories = JobCategory::count();
            $activeCategories = JobCategory::where('is_active', true)->count();
            $totalSkills = JobSkill::count();
            $activeSkills = JobSkill::where('is_active', true)->count();

            // إحصائيات هذا الشهر
            $thisMonth = Carbon::now()->startOfMonth();
            $applicationsThisMonth = JobApplication::where('created_at', '>=', $thisMonth)->count();
            $approvedThisMonth = JobApplication::where('status', 'approved')
                ->where('updated_at', '>=', $thisMonth)->count();

            // إحصائيات اليوم
            $today = Carbon::today();
            $applicationsToday = JobApplication::whereDate('created_at', $today)->count();

            // معدل الموافقة
            $approvalRate = $totalApplications > 0 ? round(($approvedApplications / $totalApplications) * 100, 2) : 0;

            return response()->json([
                'success' => true,
                'message' => 'تم جلب الإحصائيات العامة بنجاح',
                'data' => [
                    'applications' => [
                        'total' => $totalApplications,
                        'pending' => $pendingApplications,
                        'approved' => $approvedApplications,
                        'rejected' => $rejectedApplications,
                        'under_review' => $underReviewApplications,
                        'this_month' => $applicationsThisMonth,
                        'today' => $applicationsToday,
                        'approved_this_month' => $approvedThisMonth,
                        'approval_rate' => $approvalRate
                    ],
                    'categories' => [
                        'total' => $totalCategories,
                        'active' => $activeCategories,
                        'inactive' => $totalCategories - $activeCategories
                    ],
                    'skills' => [
                        'total' => $totalSkills,
                        'active' => $activeSkills,
                        'inactive' => $totalSkills - $activeSkills
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإحصائيات العامة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * إحصائيات طلبات التوظيف حسب الفترة الزمنية
     */
    public function applicationsByPeriod(Request $request): JsonResponse
    {
        try {
            $period = $request->get('period', 'month'); // day, week, month, year
            $limit = $request->get('limit', 12);

            $data = [];
            $format = '';
            $interval = '';

            switch ($period) {
                case 'day':
                    $format = '%Y-%m-%d';
                    $interval = 'DAY';
                    break;
                case 'week':
                    $format = '%Y-%u';
                    $interval = 'WEEK';
                    break;
                case 'month':
                    $format = '%Y-%m';
                    $interval = 'MONTH';
                    break;
                case 'year':
                    $format = '%Y';
                    $interval = 'YEAR';
                    break;
            }

            $applications = JobApplication::selectRaw("
                DATE_FORMAT(created_at, '{$format}') as period,
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
                SUM(CASE WHEN status = 'under_review' THEN 1 ELSE 0 END) as under_review
            ")
            ->where('created_at', '>=', Carbon::now()->sub($interval, $limit))
            ->groupBy('period')
            ->orderBy('period', 'desc')
            ->limit($limit)
            ->get();

            return response()->json([
                'success' => true,
                'message' => 'تم جلب إحصائيات طلبات التوظيف حسب الفترة بنجاح',
                'data' => [
                    'period' => $period,
                    'applications' => $applications
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب إحصائيات طلبات التوظيف',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * إحصائيات طلبات التوظيف حسب الفئات
     */
    public function applicationsByCategory(): JsonResponse
    {
        try {
            $categories = JobCategory::withCount([
                'jobApplications',
                'jobApplications as pending_count' => function ($query) {
                    $query->where('status', 'pending');
                },
                'jobApplications as approved_count' => function ($query) {
                    $query->where('status', 'approved');
                },
                'jobApplications as rejected_count' => function ($query) {
                    $query->where('status', 'rejected');
                }
            ])
            ->orderBy('job_applications_count', 'desc')
            ->get();

            return response()->json([
                'success' => true,
                'message' => 'تم جلب إحصائيات طلبات التوظيف حسب الفئات بنجاح',
                'data' => $categories
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب إحصائيات الفئات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * إحصائيات المهارات الأكثر طلباً
     */
    public function topSkills(Request $request): JsonResponse
    {
        try {
            $limit = $request->get('limit', 10);

            $skills = JobSkill::withCount('jobApplications')
                ->orderBy('job_applications_count', 'desc')
                ->limit($limit)
                ->get();

            // إضافة إحصائيات مستويات الإتقان لكل مهارة
            $skills->each(function ($skill) {
                $proficiencyStats = $skill->jobApplications()
                    ->join('job_application_skills', 'job_applications.id', '=', 'job_application_skills.job_application_id')
                    ->where('job_application_skills.job_skill_id', $skill->id)
                    ->selectRaw('proficiency_level, COUNT(*) as count')
                    ->groupBy('proficiency_level')
                    ->pluck('count', 'proficiency_level')
                    ->toArray();

                $skill->proficiency_breakdown = $proficiencyStats;
            });

            return response()->json([
                'success' => true,
                'message' => 'تم جلب إحصائيات المهارات الأكثر طلباً بنجاح',
                'data' => $skills
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب إحصائيات المهارات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * إحصائيات ديموغرافية
     */
    public function demographics(): JsonResponse
    {
        try {
            // إحصائيات الجنس
            $genderStats = JobApplication::selectRaw('gender, COUNT(*) as count')
                ->groupBy('gender')
                ->pluck('count', 'gender')
                ->toArray();

            // إحصائيات الأعمار
            $ageStats = JobApplication::selectRaw('
                CASE 
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) < 25 THEN "أقل من 25"
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 25 AND 35 THEN "25-35"
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 36 AND 45 THEN "36-45"
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 46 AND 55 THEN "46-55"
                    ELSE "أكثر من 55"
                END as age_group,
                COUNT(*) as count
            ')
            ->groupBy('age_group')
            ->pluck('count', 'age_group')
            ->toArray();

            // إحصائيات سنوات الخبرة
            $experienceStats = JobApplication::selectRaw('
                CASE 
                    WHEN years_of_experience = 0 THEN "بدون خبرة"
                    WHEN years_of_experience BETWEEN 1 AND 3 THEN "1-3 سنوات"
                    WHEN years_of_experience BETWEEN 4 AND 7 THEN "4-7 سنوات"
                    WHEN years_of_experience BETWEEN 8 AND 15 THEN "8-15 سنة"
                    ELSE "أكثر من 15 سنة"
                END as experience_group,
                COUNT(*) as count
            ')
            ->groupBy('experience_group')
            ->pluck('count', 'experience_group')
            ->toArray();

            // إحصائيات المدن
            $cityStats = JobApplication::selectRaw('city, COUNT(*) as count')
                ->groupBy('city')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->pluck('count', 'city')
                ->toArray();

            return response()->json([
                'success' => true,
                'message' => 'تم جلب الإحصائيات الديموغرافية بنجاح',
                'data' => [
                    'gender' => $genderStats,
                    'age_groups' => $ageStats,
                    'experience_groups' => $experienceStats,
                    'top_cities' => $cityStats
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإحصائيات الديموغرافية',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
