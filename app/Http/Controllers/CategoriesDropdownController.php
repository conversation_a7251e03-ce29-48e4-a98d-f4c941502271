<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CategoriesDropdownController extends Controller
{
    /**
     * إرجاع قائمة الفئات للـ dropdown
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // التحقق من وجود plugin ecommerce
            if (!class_exists('Botble\Ecommerce\Models\ProductCategory')) {
                return response()->json([]);
            }

            // جلب الفئات من قاعدة البيانات
            $categories = \Botble\Ecommerce\Models\ProductCategory::query()
                ->where('status', 'published')
                ->whereNull('parent_id') // فقط الفئات الرئيسية
                ->orderBy('order')
                ->orderBy('name')
                ->limit(20)
                ->get(['id', 'name'])
                ->map(function ($category) {
                    return [
                        'value' => $category->id,
                        'text' => $category->name,
                    ];
                });

            return response()->json($categories);
        } catch (\Exception $e) {
            // في حالة الخطأ، إرجاع قائمة فارغة
            return response()->json([]);
        }
    }
}
