<?php

namespace App\Http\Controllers;

use App\Http\Requests\JobApplicationRequest;
use App\Models\JobApplication;
use App\Models\JobCategory;
use App\Models\JobSkill;
use App\Notifications\JobApplicationSubmitted;
use Botble\ACL\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Botble\Theme\Facades\Theme;

class JobApplicationController extends Controller
{
    /**
     * عرض نموذج تقديم طلب التوظيف
     */
    public function create()
    {
        $jobCategories = JobCategory::active()->ordered()->get();
        $jobSkills = JobSkill::active()->get()->groupBy('category');

        return Theme::scope('job-applications.create', compact('jobCategories', 'jobSkills'))->render();
    }

    /**
     * عرض نموذج تعديل طلب التوظيف
     */
    public function edit(JobApplication $jobApplication)
    {
        // التأكد من أن المستخدم يملك هذا الطلب
        if ($jobApplication->customer_id !== auth('customer')->id()) {
            abort(403, 'غير مصرح لك بتعديل هذا الطلب');
        }

        // التأكد من أن الطلب قابل للتعديل
        if (!in_array($jobApplication->status, ['pending', 'under_review'])) {
            return redirect()->route('job-applications.show', $jobApplication)
                ->with('error', 'لا يمكن تعديل هذا الطلب في حالته الحالية.');
        }

        $jobCategories = JobCategory::active()->ordered()->get();
        $jobSkills = JobSkill::active()->get()->groupBy('category');
        $jobApplication->load(['skills']);

        return view('job-applications.edit', compact('jobApplication', 'jobCategories', 'jobSkills'));
    }

    /**
     * حفظ طلب التوظيف
     */
    public function store(JobApplicationRequest $request)
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            
            // رفع ملف السيرة الذاتية
            if ($request->hasFile('cv_file')) {
                $data['cv_file'] = $request->file('cv_file')->store('job-applications/cvs', 'public');
            }

            // إنشاء طلب التوظيف
            $jobApplication = JobApplication::create($data);

            // إضافة المهارات
            if ($request->has('skills') && is_array($request->skills)) {
                $skillsData = [];
                foreach ($request->skills as $index => $skillId) {
                    $skillsData[$skillId] = [
                        'proficiency_level' => $request->skill_proficiency[$index] ?? 'intermediate',
                        'years_experience' => $request->skill_experience[$index] ?? 0,
                    ];
                }
                $jobApplication->skills()->sync($skillsData);
            }

            DB::commit();

            // إرسال إشعار للأدمن
            $this->notifyAdminOfNewApplication($jobApplication);

            return redirect()->route('job-applications.show', $jobApplication)
                ->with('success', 'تم تقديم طلب التوظيف بنجاح! سيتم مراجعته من قبل الإدارة.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء تقديم الطلب. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * عرض تفاصيل طلب التوظيف
     */
    public function show(JobApplication $jobApplication)
    {
        // التأكد من أن المستخدم يملك هذا الطلب
        if ($jobApplication->customer_id !== auth('customer')->id()) {
            abort(403, 'غير مصرح لك بعرض هذا الطلب');
        }

        $jobApplication->load(['preferredJobCategory', 'skills', 'recommendations.store']);

        return Theme::scope('job-applications.show', compact('jobApplication'))->render();
    }

    /**
     * عرض قائمة طلبات التوظيف للمستخدم الحالي
     */
    public function index()
    {
        $jobApplications = JobApplication::where('customer_id', auth('customer')->id())
            ->with(['preferredJobCategory', 'recommendations'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Theme::scope('job-applications.index', compact('jobApplications'))->render();
    }

    /**
     * تحديث طلب التوظيف (للعميل)
     */
    public function update(JobApplicationRequest $request, JobApplication $jobApplication)
    {
        // التأكد من أن المستخدم يملك هذا الطلب
        if ($jobApplication->customer_id !== auth('customer')->id()) {
            abort(403, 'غير مصرح لك بتعديل هذا الطلب');
        }

        // التأكد من أن الطلب قابل للتعديل
        if (!in_array($jobApplication->status, ['pending', 'under_review'])) {
            return back()->with('error', 'لا يمكن تعديل هذا الطلب في حالته الحالية.');
        }

        try {
            DB::beginTransaction();

            $data = $request->validated();
            
            // رفع ملف السيرة الذاتية الجديد
            if ($request->hasFile('cv_file')) {
                // حذف الملف القديم
                if ($jobApplication->cv_file) {
                    Storage::disk('public')->delete($jobApplication->cv_file);
                }
                $data['cv_file'] = $request->file('cv_file')->store('job-applications/cvs', 'public');
            }

            // تحديث طلب التوظيف
            $jobApplication->update($data);

            // تحديث المهارات
            if ($request->has('skills') && is_array($request->skills)) {
                $skillsData = [];
                foreach ($request->skills as $index => $skillId) {
                    $skillsData[$skillId] = [
                        'proficiency_level' => $request->skill_proficiency[$index] ?? 'intermediate',
                        'years_experience' => $request->skill_experience[$index] ?? 0,
                    ];
                }
                $jobApplication->skills()->sync($skillsData);
            }

            DB::commit();

            return redirect()->route('job-applications.show', $jobApplication)
                ->with('success', 'تم تحديث طلب التوظيف بنجاح!');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث الطلب. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * حذف طلب التوظيف
     */
    public function destroy(JobApplication $jobApplication)
    {
        // التأكد من أن المستخدم يملك هذا الطلب
        if ($jobApplication->customer_id !== auth('customer')->id()) {
            abort(403, 'غير مصرح لك بحذف هذا الطلب');
        }

        // التأكد من أن الطلب قابل للحذف
        if ($jobApplication->status === 'recommended') {
            return back()->with('error', 'لا يمكن حذف طلب تم ترشيحه بالفعل.');
        }

        try {
            // حذف ملف السيرة الذاتية
            if ($jobApplication->cv_file) {
                Storage::disk('public')->delete($jobApplication->cv_file);
            }

            $jobApplication->delete();

            return redirect()->route('job-applications.index')
                ->with('success', 'تم حذف طلب التوظيف بنجاح.');

        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء حذف الطلب.');
        }
    }

    /**
     * إرسال إشعار للأدمن عند تقديم طلب جديد
     */
    private function notifyAdminOfNewApplication(JobApplication $jobApplication): void
    {
        try {
            // الحصول على جميع المستخدمين الذين لديهم صلاحية إدارة طلبات التوظيف
            $admins = User::whereHas('roles.permissions', function ($query) {
                $query->where('slug', 'job-applications.index');
            })->get();

            // إرسال الإشعار لجميع الأدمن
            Notification::send($admins, new JobApplicationSubmitted($jobApplication));

        } catch (\Exception $e) {
            // تسجيل الخطأ دون إيقاف العملية
            \Log::error('فشل في إرسال إشعار طلب التوظيف: ' . $e->getMessage());
        }
    }
}
