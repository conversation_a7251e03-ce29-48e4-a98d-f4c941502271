<?php

namespace App\Http\Controllers;

use App\Models\JobCategory;
use App\Models\JobSkill;
use Illuminate\Http\Request;

class TestController extends Controller
{
    /**
     * صفحة اختبار خدمة وظفني
     */
    public function jobApplicationsTest()
    {
        try {
            $jobCategories = JobCategory::active()->ordered()->get();
            $jobSkills = JobSkill::active()->get()->groupBy('category');
            
            return view('job-applications.test', compact('jobCategories', 'jobSkills'));
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ], 500);
        }
    }
    
    /**
     * صفحة تجربة نموذج طلب التوظيف
     */
    public function jobApplicationsDemo()
    {
        try {
            $jobCategories = JobCategory::active()->ordered()->get();
            $jobSkills = JobSkill::active()->get()->groupBy('category');
            
            return view('job-applications.demo', compact('jobCategories', 'jobSkills'));
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ], 500);
        }
    }
}
