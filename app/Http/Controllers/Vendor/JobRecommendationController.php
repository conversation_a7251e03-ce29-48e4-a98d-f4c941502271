<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Http\Requests\Vendor\JobRecommendationResponseRequest;
use App\Models\JobRecommendation;
use App\Notifications\VendorResponseReceived;
use Botble\ACL\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;

class JobRecommendationController extends Controller
{
    /**
     * عرض قائمة المرشحين للفيندور
     */
    public function index(Request $request)
    {
        $store = auth('customer')->user()->store;
        
        if (!$store) {
            abort(403, 'يجب أن تكون فيندور للوصول لهذه الصفحة');
        }

        $query = JobRecommendation::where('store_id', $store->id)
            ->with(['jobApplication.customer', 'jobApplication.preferredJobCategory', 'admin'])
            ->orderBy('recommended_at', 'desc');

        // تصفية حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // تصفية حسب الفئة
        if ($request->filled('category')) {
            $query->whereHas('jobApplication', function($q) use ($request) {
                $q->where('preferred_job_category_id', $request->category);
            });
        }

        $recommendations = $query->paginate(15);
        $statuses = JobRecommendation::getStatuses();

        return view('vendor.job-recommendations.index', compact('recommendations', 'statuses'));
    }

    /**
     * عرض تفاصيل المرشح
     */
    public function show(JobRecommendation $jobRecommendation)
    {
        $store = auth('customer')->user()->store;
        
        // التأكد من أن هذا الترشيح خاص بالفيندور الحالي
        if ($jobRecommendation->store_id !== $store->id) {
            abort(403, 'غير مصرح لك بعرض هذا المرشح');
        }

        // تحديث الحالة إلى "تم الاطلاع" إذا كانت "في الانتظار"
        if ($jobRecommendation->status === 'pending') {
            $jobRecommendation->update([
                'status' => 'viewed',
                'responded_at' => now()
            ]);
        }

        $jobRecommendation->load([
            'jobApplication.customer',
            'jobApplication.preferredJobCategory',
            'jobApplication.skills',
            'admin'
        ]);

        return view('vendor.job-recommendations.show', compact('jobRecommendation'));
    }

    /**
     * الرد على الترشيح
     */
    public function respond(JobRecommendationResponseRequest $request, JobRecommendation $jobRecommendation)
    {
        $store = auth('customer')->user()->store;
        
        // التأكد من أن هذا الترشيح خاص بالفيندور الحالي
        if ($jobRecommendation->store_id !== $store->id) {
            abort(403, 'غير مصرح لك بالرد على هذا الترشيح');
        }

        try {
            $jobRecommendation->update([
                'status' => $request->status,
                'vendor_response' => $request->vendor_response,
                'responded_at' => now()
            ]);

            // إرسال إشعار للأدمن
            $this->notifyOfVendorResponse($jobRecommendation);

            $message = match($request->status) {
                'interested' => 'تم تسجيل اهتمامك بالمرشح بنجاح.',
                'not_interested' => 'تم تسجيل عدم اهتمامك بالمرشح.',
                'hired' => 'تم تسجيل توظيف المرشح بنجاح.',
                default => 'تم تحديث ردك بنجاح.'
            };

            return redirect()->route('vendor.job-recommendations.show', $jobRecommendation)
                ->with('success', $message);

        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء تسجيل الرد.');
        }
    }

    /**
     * تحميل السيرة الذاتية
     */
    public function downloadCv(JobRecommendation $jobRecommendation)
    {
        $store = auth('customer')->user()->store;
        
        // التأكد من أن هذا الترشيح خاص بالفيندور الحالي
        if ($jobRecommendation->store_id !== $store->id) {
            abort(403, 'غير مصرح لك بتحميل هذا الملف');
        }

        $jobApplication = $jobRecommendation->jobApplication;
        
        if (!$jobApplication->cv_file) {
            return back()->with('error', 'لا يوجد ملف سيرة ذاتية لهذا المرشح.');
        }

        $filePath = storage_path('app/public/' . $jobApplication->cv_file);
        
        if (!file_exists($filePath)) {
            return back()->with('error', 'ملف السيرة الذاتية غير موجود.');
        }

        return response()->download($filePath, 'CV_' . $jobApplication->full_name . '.pdf');
    }

    /**
     * إحصائيات الترشيحات للفيندور
     */
    public function statistics()
    {
        $store = auth('customer')->user()->store;
        
        if (!$store) {
            abort(403, 'يجب أن تكون فيندور للوصول لهذه الصفحة');
        }

        $stats = [
            'total' => JobRecommendation::where('store_id', $store->id)->count(),
            'pending' => JobRecommendation::where('store_id', $store->id)->where('status', 'pending')->count(),
            'viewed' => JobRecommendation::where('store_id', $store->id)->where('status', 'viewed')->count(),
            'interested' => JobRecommendation::where('store_id', $store->id)->where('status', 'interested')->count(),
            'not_interested' => JobRecommendation::where('store_id', $store->id)->where('status', 'not_interested')->count(),
            'hired' => JobRecommendation::where('store_id', $store->id)->where('status', 'hired')->count(),
        ];

        $monthlyStats = JobRecommendation::where('store_id', $store->id)
            ->selectRaw('MONTH(recommended_at) as month, COUNT(*) as count')
            ->whereYear('recommended_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return view('vendor.job-recommendations.statistics', compact('stats', 'monthlyStats'));
    }

    /**
     * البحث في المرشحين
     */
    public function search(Request $request)
    {
        $store = auth('customer')->user()->store;
        
        if (!$store) {
            abort(403, 'يجب أن تكون فيندور للوصول لهذه الصفحة');
        }

        $query = JobRecommendation::where('store_id', $store->id)
            ->with(['jobApplication.customer', 'jobApplication.preferredJobCategory']);

        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('jobApplication', function($q) use ($search) {
                $q->where('full_name', 'like', '%' . $search . '%')
                  ->orWhere('email', 'like', '%' . $search . '%')
                  ->orWhere('phone', 'like', '%' . $search . '%');
            });
        }

        if ($request->filled('skills')) {
            $skills = explode(',', $request->skills);
            $query->whereHas('jobApplication.skills', function($q) use ($skills) {
                $q->whereIn('job_skills.name', $skills);
            });
        }

        $recommendations = $query->orderBy('recommended_at', 'desc')->paginate(15);

        return view('vendor.job-recommendations.search', compact('recommendations'));
    }

    /**
     * إرسال إشعار للأدمن عند رد الفيندور
     */
    private function notifyOfVendorResponse(JobRecommendation $jobRecommendation): void
    {
        try {
            // الحصول على جميع المستخدمين الذين لديهم صلاحية إدارة طلبات التوظيف
            $admins = User::whereHas('roles.permissions', function ($query) {
                $query->where('slug', 'job-applications.index');
            })->get();

            // إرسال الإشعار لجميع الأدمن
            Notification::send($admins, new VendorResponseReceived($jobRecommendation));

        } catch (\Exception $e) {
            \Log::error('فشل في إرسال إشعار رد الفيندور: ' . $e->getMessage());
        }
    }
}
