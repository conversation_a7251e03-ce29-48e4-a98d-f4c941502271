<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class JobRecommendationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->hasPermission('job-applications.recommend');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'job_application_id' => ['required', 'exists:job_applications,id'],
            'store_ids' => ['required', 'array', 'min:1'],
            'store_ids.*' => ['exists:mp_stores,id'],
            'admin_notes' => ['nullable', 'string', 'max:1000'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'job_application_id.required' => 'طلب التوظيف مطلوب',
            'job_application_id.exists' => 'طلب التوظيف غير موجود',
            'store_ids.required' => 'يجب اختيار متجر واحد على الأقل',
            'store_ids.array' => 'المتاجر يجب أن تكون في شكل مصفوفة',
            'store_ids.min' => 'يجب اختيار متجر واحد على الأقل',
            'store_ids.*.exists' => 'أحد المتاجر المحددة غير موجود',
            'admin_notes.max' => 'ملاحظات الأدمن يجب أن تكون أقل من 1000 حرف',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'job_application_id' => 'طلب التوظيف',
            'store_ids' => 'المتاجر',
            'admin_notes' => 'ملاحظات الأدمن',
        ];
    }
}
