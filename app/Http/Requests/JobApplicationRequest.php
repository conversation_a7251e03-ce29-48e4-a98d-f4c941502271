<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class JobApplicationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth('customer')->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'full_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255'],
            'phone' => ['required', 'string', 'max:20'],
            'age' => ['nullable', 'integer', 'min:16', 'max:70'],
            'gender' => ['nullable', Rule::in(['male', 'female'])],
            'education_level' => ['nullable', Rule::in([
                'high_school', 'diploma', 'bachelor', 'master', 'phd'
            ])],
            'experience_years' => ['required', 'integer', 'min:0', 'max:50'],
            'cv_file' => ['nullable', 'file', 'mimes:pdf,doc,docx', 'max:5120'], // 5MB max
            'cover_letter' => ['nullable', 'string', 'max:2000'],
            'preferred_job_category_id' => ['required', 'exists:job_categories,id'],
            'preferred_salary_min' => ['nullable', 'numeric', 'min:0'],
            'preferred_salary_max' => ['nullable', 'numeric', 'min:0', 'gte:preferred_salary_min'],
            'preferred_work_type' => ['required', Rule::in([
                'full_time', 'part_time', 'freelance', 'contract'
            ])],
            'available_immediately' => ['boolean'],
            'availability_date' => ['nullable', 'date', 'after:today'],
            'city' => ['nullable', 'string', 'max:100'],
            'can_relocate' => ['boolean'],
            'skills' => ['nullable', 'array'],
            'skills.*' => ['exists:job_skills,id'],
            'skill_proficiency' => ['nullable', 'array'],
            'skill_proficiency.*' => [Rule::in(['beginner', 'intermediate', 'advanced', 'expert'])],
            'skill_experience' => ['nullable', 'array'],
            'skill_experience.*' => ['integer', 'min:0', 'max:50'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'full_name.required' => 'الاسم الكامل مطلوب',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'phone.required' => 'رقم الهاتف مطلوب',
            'age.min' => 'العمر يجب أن يكون 16 سنة على الأقل',
            'age.max' => 'العمر يجب أن يكون 70 سنة كحد أقصى',
            'experience_years.required' => 'سنوات الخبرة مطلوبة',
            'experience_years.min' => 'سنوات الخبرة لا يمكن أن تكون أقل من 0',
            'cv_file.mimes' => 'ملف السيرة الذاتية يجب أن يكون PDF أو Word',
            'cv_file.max' => 'حجم ملف السيرة الذاتية يجب أن يكون أقل من 5 ميجابايت',
            'preferred_job_category_id.required' => 'فئة الوظيفة المفضلة مطلوبة',
            'preferred_job_category_id.exists' => 'فئة الوظيفة المحددة غير موجودة',
            'preferred_salary_max.gte' => 'الحد الأقصى للراتب يجب أن يكون أكبر من أو يساوي الحد الأدنى',
            'preferred_work_type.required' => 'نوع العمل المفضل مطلوب',
            'availability_date.after' => 'تاريخ التوفر يجب أن يكون في المستقبل',
            'skills.*.exists' => 'إحدى المهارات المحددة غير موجودة',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'full_name' => 'الاسم الكامل',
            'email' => 'البريد الإلكتروني',
            'phone' => 'رقم الهاتف',
            'age' => 'العمر',
            'gender' => 'الجنس',
            'education_level' => 'المستوى التعليمي',
            'experience_years' => 'سنوات الخبرة',
            'cv_file' => 'ملف السيرة الذاتية',
            'cover_letter' => 'خطاب التقديم',
            'preferred_job_category_id' => 'فئة الوظيفة المفضلة',
            'preferred_salary_min' => 'الحد الأدنى للراتب',
            'preferred_salary_max' => 'الحد الأقصى للراتب',
            'preferred_work_type' => 'نوع العمل المفضل',
            'available_immediately' => 'متاح فوراً',
            'availability_date' => 'تاريخ التوفر',
            'city' => 'المدينة',
            'can_relocate' => 'يمكن الانتقال',
            'skills' => 'المهارات',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'customer_id' => auth('customer')->id(),
            'available_immediately' => $this->boolean('available_immediately'),
            'can_relocate' => $this->boolean('can_relocate'),
        ]);
    }
}
