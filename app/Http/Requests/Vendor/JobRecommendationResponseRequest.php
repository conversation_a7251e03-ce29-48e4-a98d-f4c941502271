<?php

namespace App\Http\Requests\Vendor;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class JobRecommendationResponseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth('customer')->check() && auth('customer')->user()->store;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'status' => ['required', Rule::in([
                'viewed', 'interested', 'not_interested', 'hired'
            ])],
            'vendor_response' => ['nullable', 'string', 'max:1000'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'status.required' => 'الرد على الترشيح مطلوب',
            'status.in' => 'نوع الرد غير صحيح',
            'vendor_response.max' => 'رد الفيندور يجب أن يكون أقل من 1000 حرف',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'status' => 'حالة الرد',
            'vendor_response' => 'رد الفيندور',
        ];
    }
}
