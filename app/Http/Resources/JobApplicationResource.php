<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class JobApplicationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'full_name' => $this->full_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'date_of_birth' => $this->date_of_birth,
            'age' => $this->date_of_birth ? now()->diffInYears($this->date_of_birth) : null,
            'gender' => $this->gender,
            'gender_text' => $this->gender === 'male' ? 'ذكر' : 'أنثى',
            'nationality' => $this->nationality,
            'city' => $this->city,
            'address' => $this->address,
            'education_level' => $this->education_level,
            'years_of_experience' => $this->years_of_experience,
            'previous_experience' => $this->previous_experience,
            'cover_letter' => $this->cover_letter,
            'availability_date' => $this->availability_date,
            'expected_salary' => $this->expected_salary,
            'work_type_preference' => $this->work_type_preference,
            'work_type_text' => $this->getWorkTypeText(),
            'additional_notes' => $this->additional_notes,
            'status' => $this->status,
            'status_text' => $this->getStatusText(),
            'admin_notes' => $this->admin_notes,
            'application_date' => $this->application_date,
            'cv_file_url' => $this->cv_file_path ? asset('storage/' . $this->cv_file_path) : null,
            'category' => new JobCategoryResource($this->whenLoaded('category')),
            'skills' => JobSkillResource::collection($this->whenLoaded('skills')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * الحصول على نص نوع العمل
     */
    private function getWorkTypeText(): string
    {
        $types = [
            'full_time' => 'دوام كامل',
            'part_time' => 'دوام جزئي',
            'contract' => 'عقد مؤقت',
            'remote' => 'عمل عن بُعد'
        ];

        return $types[$this->work_type_preference] ?? $this->work_type_preference;
    }

    /**
     * الحصول على نص الحالة
     */
    private function getStatusText(): string
    {
        $statuses = [
            'pending' => 'قيد المراجعة',
            'under_review' => 'تحت المراجعة',
            'approved' => 'مقبول',
            'rejected' => 'مرفوض'
        ];

        return $statuses[$this->status] ?? $this->status;
    }
}
