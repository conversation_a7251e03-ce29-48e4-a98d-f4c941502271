<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class JobCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'sort_order' => $this->sort_order,
            'is_active' => $this->is_active,
            'status_text' => $this->is_active ? 'نشطة' : 'غير نشطة',
            'applications_count' => $this->when(
                isset($this->applications_count),
                $this->applications_count
            ),
            'pending_applications_count' => $this->when(
                isset($this->pending_applications_count),
                $this->pending_applications_count
            ),
            'approved_applications_count' => $this->when(
                isset($this->approved_applications_count),
                $this->approved_applications_count
            ),
            'rejected_applications_count' => $this->when(
                isset($this->rejected_applications_count),
                $this->rejected_applications_count
            ),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
