<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class JobSkillResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'category' => $this->category,
            'category_text' => $this->getCategoryText(),
            'is_active' => $this->is_active,
            'status_text' => $this->is_active ? 'نشطة' : 'غير نشطة',
            'applications_count' => $this->when(
                isset($this->applications_count),
                $this->applications_count
            ),
            'proficiency_level' => $this->when(
                isset($this->pivot) && $this->pivot,
                $this->pivot->proficiency_level ?? null
            ),
            'proficiency_text' => $this->when(
                isset($this->pivot) && $this->pivot,
                $this->getProficiencyText($this->pivot->proficiency_level ?? null)
            ),
            'years_experience' => $this->when(
                isset($this->pivot) && $this->pivot,
                $this->pivot->years_experience ?? null
            ),
            'proficiency_stats' => $this->when(
                isset($this->proficiency_stats),
                $this->proficiency_stats
            ),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * الحصول على نص الفئة
     */
    private function getCategoryText(): string
    {
        $categories = [
            'technical' => 'تقنية',
            'soft' => 'شخصية',
            'language' => 'لغة',
            'other' => 'أخرى'
        ];

        return $categories[$this->category] ?? $this->category;
    }

    /**
     * الحصول على نص مستوى الإتقان
     */
    private function getProficiencyText(?string $level): ?string
    {
        if (!$level) return null;

        $levels = [
            'beginner' => 'مبتدئ',
            'intermediate' => 'متوسط',
            'advanced' => 'متقدم',
            'expert' => 'خبير'
        ];

        return $levels[$level] ?? $level;
    }
}
