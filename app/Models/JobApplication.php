<?php

namespace App\Models;

use Botble\Ecommerce\Models\Customer;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;

class JobApplication extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'full_name',
        'email',
        'phone',
        'age',
        'gender',
        'education_level',
        'experience_years',
        'cv_file',
        'cover_letter',
        'preferred_job_category_id',
        'preferred_salary_min',
        'preferred_salary_max',
        'preferred_work_type',
        'available_immediately',
        'availability_date',
        'city',
        'can_relocate',
        'status',
        'admin_notes',
    ];

    protected $casts = [
        'age' => 'integer',
        'experience_years' => 'integer',
        'preferred_salary_min' => 'decimal:2',
        'preferred_salary_max' => 'decimal:2',
        'available_immediately' => 'boolean',
        'can_relocate' => 'boolean',
        'availability_date' => 'date',
    ];

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * العلاقة مع فئة الوظيفة المفضلة
     */
    public function preferredJobCategory(): BelongsTo
    {
        return $this->belongsTo(JobCategory::class, 'preferred_job_category_id');
    }

    /**
     * العلاقة مع المهارات (many-to-many)
     */
    public function skills(): BelongsToMany
    {
        return $this->belongsToMany(JobSkill::class, 'job_application_skills')
            ->withPivot(['proficiency_level', 'years_experience'])
            ->withTimestamps();
    }

    /**
     * العلاقة مع الترشيحات
     */
    public function recommendations(): HasMany
    {
        return $this->hasMany(JobRecommendation::class);
    }

    /**
     * الحصول على رابط السيرة الذاتية
     */
    public function getCvUrlAttribute(): ?string
    {
        return $this->cv_file ? Storage::url($this->cv_file) : null;
    }

    /**
     * الحصول على حالات الطلب
     */
    public static function getStatuses(): array
    {
        return [
            'pending' => 'في الانتظار',
            'under_review' => 'قيد المراجعة',
            'approved' => 'موافق عليه',
            'rejected' => 'مرفوض',
            'recommended' => 'تم الترشيح',
        ];
    }

    /**
     * الحصول على أنواع العمل
     */
    public static function getWorkTypes(): array
    {
        return [
            'full_time' => 'دوام كامل',
            'part_time' => 'دوام جزئي',
            'freelance' => 'عمل حر',
            'contract' => 'عقد مؤقت',
        ];
    }

    /**
     * الحصول على مستويات التعليم
     */
    public static function getEducationLevels(): array
    {
        return [
            'high_school' => 'ثانوي',
            'diploma' => 'دبلوم',
            'bachelor' => 'بكالوريوس',
            'master' => 'ماجستير',
            'phd' => 'دكتوراه',
        ];
    }

    /**
     * Scope للحالة
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope للفئة
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('preferred_job_category_id', $categoryId);
    }

    /**
     * Scope للمدينة
     */
    public function scopeByCity($query, $city)
    {
        return $query->where('city', $city);
    }
}
