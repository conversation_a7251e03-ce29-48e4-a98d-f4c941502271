<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class JobCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'name_ar',
        'description',
        'description_ar',
        'icon',
        'color',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * العلاقة مع طلبات التوظيف
     */
    public function jobApplications(): HasMany
    {
        return $this->hasMany(JobApplication::class, 'preferred_job_category_id');
    }

    /**
     * الحصول على الاسم المترجم
     */
    public function getDisplayNameAttribute(): string
    {
        return app()->getLocale() === 'ar' && $this->name_ar 
            ? $this->name_ar 
            : $this->name;
    }

    /**
     * الحصول على الوصف المترجم
     */
    public function getDisplayDescriptionAttribute(): ?string
    {
        return app()->getLocale() === 'ar' && $this->description_ar 
            ? $this->description_ar 
            : $this->description;
    }

    /**
     * Scope للفئات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للترتيب
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
