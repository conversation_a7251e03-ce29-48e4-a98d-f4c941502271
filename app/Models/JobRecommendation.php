<?php

namespace App\Models;

use Botble\Ecommerce\Models\Customer;
use Botble\Marketplace\Models\Store;
use Botble\ACL\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class JobRecommendation extends Model
{
    use HasFactory;

    protected $fillable = [
        'job_application_id',
        'store_id',
        'admin_id',
        'status',
        'admin_notes',
        'vendor_response',
        'recommended_at',
        'responded_at',
    ];

    protected $casts = [
        'recommended_at' => 'datetime',
        'responded_at' => 'datetime',
    ];

    /**
     * العلاقة مع طلب التوظيف
     */
    public function jobApplication(): BelongsTo
    {
        return $this->belongsTo(JobApplication::class);
    }

    /**
     * العلاقة مع المتجر/الفيندور
     */
    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    /**
     * العلاقة مع الأدمن الذي قام بالترشيح
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * الحصول على حالات الترشيح
     */
    public static function getStatuses(): array
    {
        return [
            'pending' => 'في الانتظار',
            'viewed' => 'تم الاطلاع',
            'interested' => 'مهتم',
            'not_interested' => 'غير مهتم',
            'hired' => 'تم التوظيف',
        ];
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'viewed' => 'info',
            'interested' => 'success',
            'not_interested' => 'danger',
            'hired' => 'primary',
            default => 'secondary',
        };
    }

    /**
     * الحصول على نص الحالة
     */
    public function getStatusTextAttribute(): string
    {
        return self::getStatuses()[$this->status] ?? $this->status;
    }

    /**
     * Scope للحالة
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope للمتجر
     */
    public function scopeByStore($query, $storeId)
    {
        return $query->where('store_id', $storeId);
    }

    /**
     * Scope للترشيحات المعلقة
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope للترشيحات المهتمة
     */
    public function scopeInterested($query)
    {
        return $query->where('status', 'interested');
    }

    /**
     * تحديد وقت الاستجابة
     */
    public function markAsResponded(): void
    {
        $this->update(['responded_at' => now()]);
    }
}
