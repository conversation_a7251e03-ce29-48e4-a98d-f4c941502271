<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class JobSkill extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'name_ar',
        'description',
        'category',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * العلاقة مع طلبات التوظيف (many-to-many)
     */
    public function jobApplications(): BelongsToMany
    {
        return $this->belongsToMany(JobApplication::class, 'job_application_skills')
            ->withPivot(['proficiency_level', 'years_experience'])
            ->withTimestamps();
    }

    /**
     * الحصول على الاسم المترجم
     */
    public function getDisplayNameAttribute(): string
    {
        return app()->getLocale() === 'ar' && $this->name_ar 
            ? $this->name_ar 
            : $this->name;
    }

    /**
     * Scope للمهارات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للتصفية حسب الفئة
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * الحصول على جميع الفئات المتاحة
     */
    public static function getCategories(): array
    {
        return [
            'technical' => 'تقني',
            'administrative' => 'إداري',
            'language' => 'لغوي',
            'creative' => 'إبداعي',
            'sales' => 'مبيعات',
            'customer_service' => 'خدمة عملاء',
            'other' => 'أخرى',
        ];
    }

    /**
     * الحصول على مستويات الإتقان
     */
    public static function getProficiencyLevels(): array
    {
        return [
            'beginner' => 'مبتدئ',
            'intermediate' => 'متوسط',
            'advanced' => 'متقدم',
            'expert' => 'خبير',
        ];
    }
}
