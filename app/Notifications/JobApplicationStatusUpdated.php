<?php

namespace App\Notifications;

use App\Models\JobApplication;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class JobApplicationStatusUpdated extends Notification implements ShouldQueue
{
    use Queueable;

    protected $jobApplication;
    protected $oldStatus;
    protected $newStatus;

    /**
     * Create a new notification instance.
     */
    public function __construct(JobApplication $jobApplication, string $oldStatus, string $newStatus)
    {
        $this->jobApplication = $jobApplication;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $statusTexts = [
            'pending' => 'في الانتظار',
            'under_review' => 'قيد المراجعة',
            'approved' => 'موافق عليه',
            'rejected' => 'مرفوض',
            'recommended' => 'تم الترشيح'
        ];

        $newStatusText = $statusTexts[$this->newStatus] ?? $this->newStatus;
        
        $mailMessage = (new MailMessage)
            ->subject('تحديث حالة طلب التوظيف الخاص بك')
            ->greeting('مرحباً ' . $this->jobApplication->full_name . '!')
            ->line('تم تحديث حالة طلب التوظيف الخاص بك.')
            ->line('**الحالة الجديدة:** ' . $newStatusText);

        if ($this->jobApplication->admin_notes) {
            $mailMessage->line('**ملاحظات الإدارة:** ' . $this->jobApplication->admin_notes);
        }

        $mailMessage->action('عرض تفاصيل الطلب', route('job-applications.show', $this->jobApplication));

        // رسائل مخصصة حسب الحالة
        switch ($this->newStatus) {
            case 'approved':
                $mailMessage->line('تهانينا! تم الموافقة على طلبك وسيتم ترشيحك للفيندورز المناسبين قريباً.');
                break;
            case 'rejected':
                $mailMessage->line('نأسف لإبلاغك أنه لم يتم قبول طلبك هذه المرة. يمكنك تقديم طلب جديد في المستقبل.');
                break;
            case 'recommended':
                $mailMessage->line('ممتاز! تم ترشيحك لبعض الفيندورز. ستتلقى إشعاراً عند استجابة أي منهم.');
                break;
            case 'under_review':
                $mailMessage->line('طلبك قيد المراجعة من قبل فريق الإدارة. سنتواصل معك قريباً.');
                break;
        }

        return $mailMessage->salutation('مع تحيات فريق وظفني');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase($notifiable): array
    {
        $statusTexts = [
            'pending' => 'في الانتظار',
            'under_review' => 'قيد المراجعة',
            'approved' => 'موافق عليه',
            'rejected' => 'مرفوض',
            'recommended' => 'تم الترشيح'
        ];

        $statusColors = [
            'pending' => 'warning',
            'under_review' => 'info',
            'approved' => 'success',
            'rejected' => 'danger',
            'recommended' => 'primary'
        ];

        return [
            'type' => 'job_application_status_updated',
            'title' => 'تحديث حالة طلب التوظيف',
            'message' => 'تم تحديث حالة طلب التوظيف إلى: ' . ($statusTexts[$this->newStatus] ?? $this->newStatus),
            'job_application_id' => $this->jobApplication->id,
            'old_status' => $this->oldStatus,
            'new_status' => $this->newStatus,
            'admin_notes' => $this->jobApplication->admin_notes,
            'action_url' => route('job-applications.show', $this->jobApplication),
            'icon' => 'fas fa-bell',
            'color' => $statusColors[$this->newStatus] ?? 'secondary'
        ];
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return $this->toDatabase($notifiable);
    }
}
