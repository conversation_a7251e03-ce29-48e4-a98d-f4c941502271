<?php

namespace App\Notifications;

use App\Models\JobApplication;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\DatabaseMessage;
use Illuminate\Notifications\Notification;

class JobApplicationSubmitted extends Notification implements ShouldQueue
{
    use Queueable;

    protected $jobApplication;

    /**
     * Create a new notification instance.
     */
    public function __construct(JobApplication $jobApplication)
    {
        $this->jobApplication = $jobApplication;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('طلب توظيف جديد - ' . $this->jobApplication->full_name)
            ->greeting('مرحباً!')
            ->line('تم تقديم طلب توظيف جديد في النظام.')
            ->line('**تفاصيل المتقدم:**')
            ->line('الاسم: ' . $this->jobApplication->full_name)
            ->line('البريد الإلكتروني: ' . $this->jobApplication->email)
            ->line('رقم الهاتف: ' . $this->jobApplication->phone)
            ->line('فئة الوظيفة: ' . ($this->jobApplication->preferredJobCategory->display_name ?? 'غير محدد'))
            ->line('سنوات الخبرة: ' . $this->jobApplication->experience_years . ' سنوات')
            ->action('مراجعة الطلب', route('admin.job-applications.show', $this->jobApplication))
            ->line('يرجى مراجعة الطلب واتخاذ الإجراء المناسب.')
            ->salutation('مع تحيات فريق إدارة النظام');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase($notifiable): array
    {
        return [
            'type' => 'job_application_submitted',
            'title' => 'طلب توظيف جديد',
            'message' => 'تم تقديم طلب توظيف جديد من ' . $this->jobApplication->full_name,
            'job_application_id' => $this->jobApplication->id,
            'applicant_name' => $this->jobApplication->full_name,
            'applicant_email' => $this->jobApplication->email,
            'job_category' => $this->jobApplication->preferredJobCategory->display_name ?? null,
            'experience_years' => $this->jobApplication->experience_years,
            'action_url' => route('admin.job-applications.show', $this->jobApplication),
            'icon' => 'fas fa-briefcase',
            'color' => 'primary'
        ];
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return $this->toDatabase($notifiable);
    }
}
