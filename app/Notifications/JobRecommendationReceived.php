<?php

namespace App\Notifications;

use App\Models\JobRecommendation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class JobRecommendationReceived extends Notification implements ShouldQueue
{
    use Queueable;

    protected $jobRecommendation;

    /**
     * Create a new notification instance.
     */
    public function __construct(JobRecommendation $jobRecommendation)
    {
        $this->jobRecommendation = $jobRecommendation;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $jobApplication = $this->jobRecommendation->jobApplication;
        
        return (new MailMessage)
            ->subject('مرشح جديد للتوظيف - ' . $jobApplication->full_name)
            ->greeting('مرحباً!')
            ->line('تم ترشيح مرشح جديد لمتجرك من قبل الإدارة.')
            ->line('**تفاصيل المرشح:**')
            ->line('الاسم: ' . $jobApplication->full_name)
            ->line('البريد الإلكتروني: ' . $jobApplication->email)
            ->line('رقم الهاتف: ' . $jobApplication->phone)
            ->line('فئة الوظيفة: ' . ($jobApplication->preferredJobCategory->display_name ?? 'غير محدد'))
            ->line('سنوات الخبرة: ' . $jobApplication->experience_years . ' سنوات')
            ->line('نوع العمل المفضل: ' . $this->getWorkTypeText($jobApplication->preferred_work_type))
            ->when($jobApplication->preferred_salary_min || $jobApplication->preferred_salary_max, function ($mail) use ($jobApplication) {
                $salaryRange = '';
                if ($jobApplication->preferred_salary_min && $jobApplication->preferred_salary_max) {
                    $salaryRange = number_format($jobApplication->preferred_salary_min) . ' - ' . number_format($jobApplication->preferred_salary_max) . ' ريال';
                } elseif ($jobApplication->preferred_salary_min) {
                    $salaryRange = 'من ' . number_format($jobApplication->preferred_salary_min) . ' ريال';
                } else {
                    $salaryRange = 'حتى ' . number_format($jobApplication->preferred_salary_max) . ' ريال';
                }
                return $mail->line('النطاق المرغوب للراتب: ' . $salaryRange);
            })
            ->when($this->jobRecommendation->admin_notes, function ($mail) {
                return $mail->line('**ملاحظات الإدارة:** ' . $this->jobRecommendation->admin_notes);
            })
            ->action('مراجعة المرشح', route('vendor.job-recommendations.show', $this->jobRecommendation))
            ->line('يرجى مراجعة المرشح والرد على الترشيح.')
            ->salutation('مع تحيات فريق وظفني');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase($notifiable): array
    {
        $jobApplication = $this->jobRecommendation->jobApplication;
        
        return [
            'type' => 'job_recommendation_received',
            'title' => 'مرشح جديد للتوظيف',
            'message' => 'تم ترشيح ' . $jobApplication->full_name . ' لمتجرك',
            'job_recommendation_id' => $this->jobRecommendation->id,
            'job_application_id' => $jobApplication->id,
            'applicant_name' => $jobApplication->full_name,
            'applicant_email' => $jobApplication->email,
            'job_category' => $jobApplication->preferredJobCategory->display_name ?? null,
            'experience_years' => $jobApplication->experience_years,
            'admin_notes' => $this->jobRecommendation->admin_notes,
            'action_url' => route('vendor.job-recommendations.show', $this->jobRecommendation),
            'icon' => 'fas fa-user-plus',
            'color' => 'success'
        ];
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return $this->toDatabase($notifiable);
    }

    /**
     * Get work type text in Arabic
     */
    private function getWorkTypeText(string $workType): string
    {
        $workTypes = [
            'full_time' => 'دوام كامل',
            'part_time' => 'دوام جزئي',
            'freelance' => 'عمل حر',
            'contract' => 'عقد مؤقت'
        ];

        return $workTypes[$workType] ?? $workType;
    }
}
