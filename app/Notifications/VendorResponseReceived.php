<?php

namespace App\Notifications;

use App\Models\JobRecommendation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class VendorResponseReceived extends Notification implements ShouldQueue
{
    use Queueable;

    protected $jobRecommendation;

    /**
     * Create a new notification instance.
     */
    public function __construct(JobRecommendation $jobRecommendation)
    {
        $this->jobRecommendation = $jobRecommendation;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $jobApplication = $this->jobRecommendation->jobApplication;
        $store = $this->jobRecommendation->store;
        
        $statusTexts = [
            'viewed' => 'تم الاطلاع',
            'interested' => 'مهتم',
            'not_interested' => 'غير مهتم',
            'hired' => 'تم التوظيف'
        ];

        $statusText = $statusTexts[$this->jobRecommendation->status] ?? $this->jobRecommendation->status;
        
        $mailMessage = (new MailMessage)
            ->subject('رد من فيندور على ترشيح ' . $jobApplication->full_name)
            ->greeting('مرحباً!')
            ->line('تلقيت رداً من فيندور على ترشيح أحد المرشحين.')
            ->line('**تفاصيل الترشيح:**')
            ->line('المرشح: ' . $jobApplication->full_name)
            ->line('الفيندور: ' . $store->name)
            ->line('الرد: ' . $statusText);

        if ($this->jobRecommendation->vendor_response) {
            $mailMessage->line('**رسالة الفيندور:** ' . $this->jobRecommendation->vendor_response);
        }

        // رسائل مخصصة حسب نوع الرد
        switch ($this->jobRecommendation->status) {
            case 'interested':
                $mailMessage->line('الفيندور مهتم بالمرشح! يمكنك متابعة عملية التوظيف.');
                break;
            case 'not_interested':
                $mailMessage->line('الفيندور غير مهتم بالمرشح. يمكنك ترشيحه لفيندورز آخرين.');
                break;
            case 'hired':
                $mailMessage->line('تهانينا! تم توظيف المرشح بنجاح.');
                break;
        }

        return $mailMessage
            ->action('مراجعة التفاصيل', route('admin.job-applications.show', $jobApplication))
            ->salutation('مع تحيات فريق وظفني');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase($notifiable): array
    {
        $jobApplication = $this->jobRecommendation->jobApplication;
        $store = $this->jobRecommendation->store;
        
        $statusTexts = [
            'viewed' => 'تم الاطلاع',
            'interested' => 'مهتم',
            'not_interested' => 'غير مهتم',
            'hired' => 'تم التوظيف'
        ];

        $statusColors = [
            'viewed' => 'info',
            'interested' => 'success',
            'not_interested' => 'danger',
            'hired' => 'primary'
        ];

        return [
            'type' => 'vendor_response_received',
            'title' => 'رد من فيندور',
            'message' => $store->name . ' رد على ترشيح ' . $jobApplication->full_name . ': ' . ($statusTexts[$this->jobRecommendation->status] ?? $this->jobRecommendation->status),
            'job_recommendation_id' => $this->jobRecommendation->id,
            'job_application_id' => $jobApplication->id,
            'store_id' => $store->id,
            'store_name' => $store->name,
            'applicant_name' => $jobApplication->full_name,
            'response_status' => $this->jobRecommendation->status,
            'vendor_response' => $this->jobRecommendation->vendor_response,
            'action_url' => route('admin.job-applications.show', $jobApplication),
            'icon' => 'fas fa-reply',
            'color' => $statusColors[$this->jobRecommendation->status] ?? 'secondary'
        ];
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return $this->toDatabase($notifiable);
    }
}
