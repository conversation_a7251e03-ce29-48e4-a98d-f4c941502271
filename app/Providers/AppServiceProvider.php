<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // تحميل الدوال المساعدة للترجمة
        if (file_exists(app_path('Helpers/LocalizationHelper.php'))) {
            require_once app_path('Helpers/LocalizationHelper.php');
        }

        // تحميل الدوال المساعدة للترقيم الصفحي
        if (file_exists(app_path('Helpers/PaginationHelper.php'))) {
            require_once app_path('Helpers/PaginationHelper.php');
        }
    }
}
