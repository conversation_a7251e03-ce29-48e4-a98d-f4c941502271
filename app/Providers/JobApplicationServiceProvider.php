<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Botble\Base\Facades\DashboardMenu;

class JobApplicationServiceProvider extends ServiceProvider
{

    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تحميل الصلاحيات
        $this->loadPermissions();

        // إضافة جميع القوائم
        $this->addAdminMenu();
        $this->addCustomerMenu();
        $this->addVendorMenu();
    }

    /**
     * تحميل الصلاحيات
     */
    protected function loadPermissions(): void
    {
        if (file_exists(config_path('permissions/job-applications.php'))) {
            config(['permissions.job-applications' => require config_path('permissions/job-applications.php')]);
        }
    }

    /**
     * إضافة قائمة الأدمن
     */
    protected function addAdminMenu(): void
    {
        DashboardMenu::default()->beforeRetrieving(function () {
            DashboardMenu::make()
                ->registerItem([
                    'id' => 'cms-plugins-job-applications',
                    'priority' => 120,
                    'parent_id' => null,
                    'name' => 'خدمة وظفني',
                    'icon' => 'ti ti-briefcase',
                    'url' => url('admin/job-applications'),
                    'permissions' => [],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-job-applications-list',
                    'priority' => 1,
                    'parent_id' => 'cms-plugins-job-applications',
                    'name' => 'طلبات التوظيف',
                    'icon' => 'ti ti-list',
                    'url' => url('admin/job-applications'),
                    'permissions' => [],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-job-applications-statistics',
                    'priority' => 2,
                    'parent_id' => 'cms-plugins-job-applications',
                    'name' => 'الإحصائيات',
                    'icon' => 'ti ti-chart-bar',
                    'url' => url('admin/job-applications/statistics/overview'),
                    'permissions' => [],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-job-categories',
                    'priority' => 3,
                    'parent_id' => 'cms-plugins-job-applications',
                    'name' => 'فئات الوظائف',
                    'icon' => 'ti ti-tags',
                    'url' => url('admin/job-categories'),
                    'permissions' => [],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-job-skills',
                    'priority' => 4,
                    'parent_id' => 'cms-plugins-job-applications',
                    'name' => 'مهارات الوظائف',
                    'icon' => 'ti ti-settings',
                    'url' => url('admin/job-skills'),
                    'permissions' => ['job-skills.index'],
                ]);
        });
    }

    /**
     * إضافة قائمة العملاء
     */
    protected function addCustomerMenu(): void
    {
        DashboardMenu::for('customer')->beforeRetrieving(function () {
            DashboardMenu::make()
                ->registerItem([
                    'id' => 'cms-plugins-job-applications-customer',
                    'priority' => 25,
                    'parent_id' => null,
                    'name' => 'وظفني',
                    'icon' => 'ti ti-briefcase',
                    'url' => url('customer/job-applications'),
                    'permissions' => [],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-job-applications-customer-list',
                    'priority' => 1,
                    'parent_id' => 'cms-plugins-job-applications-customer',
                    'name' => 'طلباتي',
                    'icon' => 'ti ti-list',
                    'url' => url('customer/job-applications'),
                    'permissions' => [],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-job-applications-customer-create',
                    'priority' => 2,
                    'parent_id' => 'cms-plugins-job-applications-customer',
                    'name' => 'تقديم طلب جديد',
                    'icon' => 'ti ti-plus',
                    'url' => url('customer/job-applications/create'),
                    'permissions' => [],
                ]);
        });
    }

    /**
     * إضافة قائمة الفيندورز
     */
    protected function addVendorMenu(): void
    {
        DashboardMenu::for('vendor')->registerItem([
            'id' => 'cms-plugins-job-recommendations-vendor',
            'priority' => 30,
            'parent_id' => null,
            'name' => 'المرشحين للتوظيف',
            'icon' => 'fas fa-user-plus',
            'url' => url('vendor/job-recommendations'),
            'permissions' => [],
        ])->registerItem([
            'id' => 'cms-plugins-job-recommendations-vendor-list',
            'priority' => 1,
            'parent_id' => 'cms-plugins-job-recommendations-vendor',
            'name' => 'قائمة المرشحين',
            'icon' => 'fas fa-list',
            'url' => url('vendor/job-recommendations'),
            'permissions' => [],
        ])->registerItem([
            'id' => 'cms-plugins-job-recommendations-vendor-statistics',
            'priority' => 2,
            'parent_id' => 'cms-plugins-job-recommendations-vendor',
            'name' => 'الإحصائيات',
            'icon' => 'fas fa-chart-bar',
            'url' => url('vendor/job-recommendations/statistics/overview'),
            'permissions' => [],
        ])->registerItem([
            'id' => 'cms-plugins-job-recommendations-vendor-search',
            'priority' => 3,
            'parent_id' => 'cms-plugins-job-recommendations-vendor',
            'name' => 'البحث في المرشحين',
            'icon' => 'fas fa-search',
            'url' => url('vendor/job-recommendations/search/candidates'),
            'permissions' => [],
        ]);
    }
}
