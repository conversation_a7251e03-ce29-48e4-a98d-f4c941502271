<?php
/**
 * إصلاح مشكلة مجلدات storage بعد رفع المشروع
 * ارفع هذا الملف إلى الخادم وشغله لإصلاح المشكلة
 */

echo "<h1>🔧 إصلاح مجلدات Storage</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>";

// قائمة المجلدات المطلوبة
$directories = [
    'storage',
    'storage/app',
    'storage/app/public',
    'storage/framework',
    'storage/framework/cache',
    'storage/framework/cache/data',
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/logs',
    'bootstrap/cache'
];

echo "<h2>📁 إنشاء المجلدات المطلوبة</h2>";

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0777, true)) {
            echo "<p class='success'>✅ تم إنشاء: $dir</p>";
        } else {
            echo "<p class='error'>❌ فشل في إنشاء: $dir</p>";
        }
    } else {
        echo "<p class='info'>ℹ️ موجود بالفعل: $dir</p>";
    }
}

echo "<h2>🔐 ضبط الصلاحيات</h2>";

// ضبط الصلاحيات
$permissions = [
    'storage' => 0777,
    'bootstrap/cache' => 0777,
    '.env' => 0644
];

foreach ($permissions as $path => $permission) {
    if (file_exists($path)) {
        if (chmod($path, $permission)) {
            echo "<p class='success'>✅ تم ضبط صلاحيات: $path (" . decoct($permission) . ")</p>";
        } else {
            echo "<p class='error'>❌ فشل في ضبط صلاحيات: $path</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ غير موجود: $path</p>";
    }
}

// إنشاء ملفات فارغة مطلوبة
echo "<h2>📄 إنشاء ملفات فارغة مطلوبة</h2>";

$files = [
    'storage/logs/laravel.log',
    'storage/framework/sessions/.gitignore',
    'storage/framework/cache/data/.gitignore',
    'storage/framework/views/.gitignore'
];

foreach ($files as $file) {
    $dir = dirname($file);
    if (!is_dir($dir)) {
        mkdir($dir, 0777, true);
    }
    
    if (!file_exists($file)) {
        if (touch($file)) {
            echo "<p class='success'>✅ تم إنشاء: $file</p>";
        } else {
            echo "<p class='error'>❌ فشل في إنشاء: $file</p>";
        }
    } else {
        echo "<p class='info'>ℹ️ موجود بالفعل: $file</p>";
    }
}

// فحص إعدادات PHP
echo "<h2>🔍 فحص إعدادات PHP</h2>";

$phpChecks = [
    'file_uploads' => ini_get('file_uploads'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time')
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>الإعداد</th><th>القيمة</th></tr>";
foreach ($phpChecks as $setting => $value) {
    echo "<tr><td>$setting</td><td>$value</td></tr>";
}
echo "</table>";

// اختبار كتابة ملف
echo "<h2>✍️ اختبار كتابة الملفات</h2>";

$testFile = 'storage/framework/cache/data/test_write.txt';
$testContent = 'Test write: ' . date('Y-m-d H:i:s');

if (file_put_contents($testFile, $testContent)) {
    echo "<p class='success'>✅ اختبار الكتابة نجح</p>";
    unlink($testFile);
} else {
    echo "<p class='error'>❌ اختبار الكتابة فشل</p>";
}

// فحص ملف .env
echo "<h2>⚙️ فحص ملف .env</h2>";

if (file_exists('.env')) {
    echo "<p class='success'>✅ ملف .env موجود</p>";
    
    // فحص إعدادات قاعدة البيانات
    $envContent = file_get_contents('.env');
    preg_match('/DB_HOST=(.*)/', $envContent, $hostMatch);
    preg_match('/DB_DATABASE=(.*)/', $envContent, $dbMatch);
    
    $dbHost = isset($hostMatch[1]) ? trim($hostMatch[1], '"') : 'غير محدد';
    $dbName = isset($dbMatch[1]) ? trim($dbMatch[1], '"') : 'غير محدد';
    
    echo "<p class='info'>DB_HOST: $dbHost</p>";
    echo "<p class='info'>DB_DATABASE: $dbName</p>";
} else {
    echo "<p class='error'>❌ ملف .env غير موجود</p>";
}

echo "<h2>🚀 الخطوات التالية</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<h3>إذا تم إصلاح المشاكل:</h3>";
echo "<ol>";
echo "<li>اختبر الموقع: <a href='/' target='_blank'>الصفحة الرئيسية</a></li>";
echo "<li>اختبر لوحة الإدارة: <a href='/admin' target='_blank'>لوحة الإدارة</a></li>";
echo "<li>احذف هذا الملف بعد التأكد من عمل كل شيء</li>";
echo "</ol>";

echo "<h3>إذا استمرت المشاكل:</h3>";
echo "<ul>";
echo "<li>تأكد من أن Document Root يشير إلى مجلد <code>public</code></li>";
echo "<li>شغل هذه الأوامر عبر SSH أو Terminal:</li>";
echo "</ul>";

echo "<pre>";
echo "chmod -R 777 storage/\n";
echo "chmod -R 777 bootstrap/cache/\n";
echo "mkdir -p storage/framework/cache/data\n";
echo "mkdir -p storage/framework/sessions\n";
echo "mkdir -p storage/framework/views\n";
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
echo "<p><small>⚠️ احذف هذا الملف بعد إصلاح المشاكل</small></p>";
?>
