#!/bin/bash

echo "🔧 إصلاح مجلدات Storage على Hostinger"
echo "======================================"

# التحقق من المجلد الحالي
if [ ! -f "artisan" ]; then
    echo "❌ هذا السكريبت يجب تشغيله في مجلد المشروع الرئيسي"
    exit 1
fi

echo "✅ تم العثور على مجلد المشروع"

# إنشاء المجلدات المطلوبة
echo "📁 إنشاء المجلدات المطلوبة..."

directories=(
    "storage"
    "storage/app"
    "storage/app/public"
    "storage/framework"
    "storage/framework/cache"
    "storage/framework/cache/data"
    "storage/framework/sessions"
    "storage/framework/views"
    "storage/logs"
    "bootstrap/cache"
)

for dir in "${directories[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo "✅ تم إنشاء: $dir"
    else
        echo "ℹ️ موجود بالفعل: $dir"
    fi
done

# ضبط الصلاحيات
echo "🔐 ضبط الصلاحيات..."

chmod -R 755 ./
chmod -R 777 storage/
chmod -R 777 bootstrap/cache/
chmod 644 .env 2>/dev/null || echo "⚠️ ملف .env غير موجود"

echo "✅ تم ضبط الصلاحيات"

# إنشاء ملفات فارغة مطلوبة
echo "📄 إنشاء ملفات فارغة مطلوبة..."

touch storage/logs/laravel.log
touch storage/framework/sessions/.gitignore
touch storage/framework/cache/data/.gitignore
touch storage/framework/views/.gitignore

echo "✅ تم إنشاء الملفات الفارغة"

# تنظيف الكاش
echo "🧹 تنظيف الكاش..."

php artisan config:clear 2>/dev/null || echo "⚠️ تخطي config:clear"
php artisan route:clear 2>/dev/null || echo "⚠️ تخطي route:clear"
php artisan view:clear 2>/dev/null || echo "⚠️ تخطي view:clear"
php artisan cache:clear 2>/dev/null || echo "⚠️ تخطي cache:clear"

echo "✅ تم تنظيف الكاش"

# اختبار كتابة ملف
echo "✍️ اختبار كتابة الملفات..."

test_file="storage/framework/cache/data/test_write_$(date +%s).txt"
test_content="Test write: $(date)"

if echo "$test_content" > "$test_file"; then
    echo "✅ اختبار الكتابة نجح"
    rm -f "$test_file"
else
    echo "❌ اختبار الكتابة فشل"
fi

# فحص ملف .env
echo "⚙️ فحص ملف .env..."

if [ -f ".env" ]; then
    echo "✅ ملف .env موجود"
    
    # استخراج إعدادات قاعدة البيانات
    db_host=$(grep "^DB_HOST=" .env | cut -d'=' -f2 | tr -d '"')
    db_name=$(grep "^DB_DATABASE=" .env | cut -d'=' -f2 | tr -d '"')
    
    echo "ℹ️ DB_HOST: $db_host"
    echo "ℹ️ DB_DATABASE: $db_name"
else
    echo "❌ ملف .env غير موجود"
fi

# فحص إعدادات PHP
echo "🔍 فحص إعدادات PHP..."
php_version=$(php -v | head -n 1)
echo "ℹ️ $php_version"

# إنشاء رابط التخزين
echo "🔗 إنشاء رابط التخزين..."
php artisan storage:link 2>/dev/null || echo "⚠️ تخطي storage:link"

echo ""
echo "🎉 تم الانتهاء من الإصلاح!"
echo ""
echo "📋 ملخص ما تم:"
echo "- ✅ إنشاء جميع مجلدات storage المطلوبة"
echo "- ✅ ضبط الصلاحيات (777 لـ storage و bootstrap/cache)"
echo "- ✅ إنشاء الملفات الفارغة المطلوبة"
echo "- ✅ تنظيف الكاش"
echo "- ✅ اختبار كتابة الملفات"
echo ""
echo "🚀 الخطوات التالية:"
echo "1. اختبر الموقع: https://artbella.online/"
echo "2. اختبر لوحة الإدارة: https://artbella.online/admin"
echo "3. إذا استمرت المشاكل، تحقق من سجلات الأخطاء في storage/logs/"
echo ""
echo "💡 نصائح:"
echo "- تأكد من أن Document Root يشير إلى مجلد public"
echo "- تحقق من إعدادات قاعدة البيانات في .env"
echo "- تأكد من أن PHP 8.1+ مثبت"
