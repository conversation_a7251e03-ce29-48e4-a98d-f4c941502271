<?php

echo "💄 إعداد فئات ومنتجات العناية والجمال\n\n";

// إعداد Laravel
$autoload = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoload)) {
    require_once $autoload;
    
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} else {
    echo "❌ لا يمكن العثور على autoload.php\n";
    exit;
}

// 1. مسح الفئات الحالية
echo "1. مسح الفئات الحالية:\n";
try {
    $deletedCategories = \Botble\Ecommerce\Models\ProductCategory::count();
    \Botble\Ecommerce\Models\ProductCategory::truncate();
    echo "  ✅ تم مسح {$deletedCategories} فئة\n";
} catch (Exception $e) {
    echo "  ❌ خطأ في مسح الفئات: " . $e->getMessage() . "\n";
}

// 2. إضافة فئات العناية والجمال
echo "\n2. إضافة فئات العناية والجمال:\n";

$beautyCategories = [
    // الفئات الرئيسية
    [
        'name' => 'العناية بالبشرة',
        'description' => 'منتجات العناية بالبشرة والوجه',
        'parent_id' => 0,
        'order' => 1,
        'is_featured' => true,
        'icon' => 'fas fa-spa',
        'children' => [
            'كريمات الوجه',
            'سيروم البشرة',
            'ماسكات الوجه',
            'منظفات البشرة',
            'تونر ومقشرات',
            'كريمات العين',
            'واقي الشمس'
        ]
    ],
    [
        'name' => 'العناية بالشعر',
        'description' => 'منتجات العناية بالشعر وفروة الرأس',
        'parent_id' => 0,
        'order' => 2,
        'is_featured' => true,
        'icon' => 'fas fa-cut',
        'children' => [
            'شامبو',
            'بلسم',
            'ماسكات الشعر',
            'زيوت الشعر',
            'سيروم الشعر',
            'منتجات التصفيف',
            'علاج تساقط الشعر'
        ]
    ],
    [
        'name' => 'المكياج',
        'description' => 'مستحضرات التجميل والمكياج',
        'parent_id' => 0,
        'order' => 3,
        'is_featured' => true,
        'icon' => 'fas fa-palette',
        'children' => [
            'كريم الأساس',
            'الكونسيلر',
            'البودرة',
            'أحمر الشفاه',
            'ظلال العيون',
            'الماسكارا',
            'محدد العيون',
            'أحمر الخدود'
        ]
    ],
    [
        'name' => 'العطور',
        'description' => 'العطور ومزيلات العرق',
        'parent_id' => 0,
        'order' => 4,
        'is_featured' => true,
        'icon' => 'fas fa-spray-can',
        'children' => [
            'عطور نسائية',
            'عطور رجالية',
            'عطور مشتركة',
            'مزيلات العرق',
            'بخاخات الجسم'
        ]
    ],
    [
        'name' => 'العناية بالجسم',
        'description' => 'منتجات العناية بالجسم والاستحمام',
        'parent_id' => 0,
        'order' => 5,
        'is_featured' => true,
        'icon' => 'fas fa-bath',
        'children' => [
            'كريمات الجسم',
            'لوشن الجسم',
            'صابون الاستحمام',
            'مقشرات الجسم',
            'زيوت الجسم'
        ]
    ],
    [
        'name' => 'العناية بالأظافر',
        'description' => 'منتجات العناية بالأظافر',
        'parent_id' => 0,
        'order' => 6,
        'is_featured' => false,
        'icon' => 'fas fa-hand-sparkles',
        'children' => [
            'طلاء الأظافر',
            'مقويات الأظافر',
            'مزيل طلاء الأظافر',
            'أدوات الأظافر'
        ]
    ]
];

foreach ($beautyCategories as $categoryData) {
    try {
        // إنشاء الفئة الرئيسية
        $parentCategory = \Botble\Ecommerce\Models\ProductCategory::create([
            'name' => $categoryData['name'],
            'description' => $categoryData['description'],
            'parent_id' => $categoryData['parent_id'],
            'order' => $categoryData['order'],
            'status' => 'published',
            'is_featured' => $categoryData['is_featured'],
            'icon' => $categoryData['icon']
        ]);
        
        echo "  ✅ تم إنشاء الفئة الرئيسية: {$categoryData['name']}\n";
        
        // إنشاء الفئات الفرعية
        foreach ($categoryData['children'] as $index => $childName) {
            \Botble\Ecommerce\Models\ProductCategory::create([
                'name' => $childName,
                'description' => "منتجات {$childName}",
                'parent_id' => $parentCategory->id,
                'order' => $index + 1,
                'status' => 'published',
                'is_featured' => false
            ]);
            echo "    ✅ تم إنشاء الفئة الفرعية: {$childName}\n";
        }
        
    } catch (Exception $e) {
        echo "  ❌ خطأ في إنشاء الفئة {$categoryData['name']}: " . $e->getMessage() . "\n";
    }
}

echo "\n✅ تم إنشاء جميع فئات العناية والجمال بنجاح!\n";
echo "\n📊 إحصائيات:\n";
echo "  - الفئات الرئيسية: 6\n";
echo "  - الفئات الفرعية: " . (7+7+8+5+5+4) . "\n";
echo "  - إجمالي الفئات: " . \Botble\Ecommerce\Models\ProductCategory::count() . "\n";

echo "\n🎉 تم الانتهاء من إعداد فئات العناية والجمال!\n";
