<?php

echo "🎓 إعداد دورات العناية والجمال\n\n";

// إعداد Laravel
$autoload = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoload)) {
    require_once $autoload;
    
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} else {
    echo "❌ لا يمكن العثور على autoload.php\n";
    exit;
}

// 1. مسح فئات الدورات الحالية وإضافة فئات جديدة
echo "1. إعداد فئات الدورات:\n";
try {
    \Botble\TrainingAppointment\Models\CourseCategory::truncate();
    
    $courseCategories = [
        [
            'name' => 'دورات المكياج',
            'description' => 'تعلم فنون المكياج الاحترافي',
            'order' => 1,
            'status' => 'published'
        ],
        [
            'name' => 'دورات العناية بالبشرة',
            'description' => 'تعلم أساسيات العناية بالبشرة',
            'order' => 2,
            'status' => 'published'
        ],
        [
            'name' => 'دورات العناية بالشعر',
            'description' => 'تعلم تقنيات العناية بالشعر وتصفيفه',
            'order' => 3,
            'status' => 'published'
        ],
        [
            'name' => 'دورات الأظافر',
            'description' => 'تعلم فن تجميل الأظافر',
            'order' => 4,
            'status' => 'published'
        ]
    ];
    
    foreach ($courseCategories as $categoryData) {
        \Botble\TrainingAppointment\Models\CourseCategory::create($categoryData);
        echo "  ✅ تم إنشاء فئة الدورة: {$categoryData['name']}\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ خطأ في إنشاء فئات الدورات: " . $e->getMessage() . "\n";
}

// 2. مسح الدورات الحالية وإضافة دورات جديدة
echo "\n2. إضافة دورات العناية والجمال:\n";
try {
    \Botble\TrainingAppointment\Models\Course::truncate();
    
    $categories = \Botble\TrainingAppointment\Models\CourseCategory::all()->keyBy('name');
    
    $beautyCourses = [
        [
            'title' => 'دورة المكياج الأساسي للمبتدئين',
            'description' => 'تعلم أساسيات المكياج من الصفر حتى الاحتراف',
            'content' => 'دورة شاملة تغطي جميع أساسيات المكياج بدءاً من تحضير البشرة وحتى المكياج المسائي. ستتعلم استخدام الأدوات المختلفة وتقنيات التطبيق الصحيحة.',
            'price' => 500,
            'max_students' => 15,
            'duration_hours' => 20,
            'start_date' => '2024-02-01',
            'end_date' => '2024-02-15',
            'location' => 'مركز التدريب الرئيسي',
            'is_online' => false,
            'category' => 'دورات المكياج',
            'image' => 'courses/makeup-basic.jpg'
        ],
        [
            'title' => 'دورة المكياج المتقدم والمناسبات',
            'description' => 'تعلم تقنيات المكياج المتقدمة للمناسبات الخاصة',
            'content' => 'دورة متقدمة للراغبين في تطوير مهاراتهم في المكياج. تشمل مكياج العرائس، المناسبات، والتصوير الفوتوغرافي.',
            'price' => 800,
            'max_students' => 10,
            'duration_hours' => 30,
            'start_date' => '2024-02-10',
            'end_date' => '2024-03-01',
            'location' => 'استوديو المكياج المتخصص',
            'is_online' => false,
            'category' => 'دورات المكياج',
            'image' => 'courses/makeup-advanced.jpg'
        ],
        [
            'title' => 'دورة العناية بالبشرة والتنظيف العميق',
            'description' => 'تعلم تقنيات العناية بالبشرة والتنظيف الاحترافي',
            'content' => 'دورة متخصصة في العناية بالبشرة تشمل تحليل أنواع البشرة، تقنيات التنظيف العميق، والعلاجات الطبيعية.',
            'price' => 600,
            'max_students' => 12,
            'duration_hours' => 25,
            'start_date' => '2024-02-05',
            'end_date' => '2024-02-20',
            'location' => 'مركز العناية بالبشرة',
            'is_online' => false,
            'category' => 'دورات العناية بالبشرة',
            'image' => 'courses/skincare.jpg'
        ],
        [
            'title' => 'دورة تصفيف الشعر والتسريحات',
            'description' => 'تعلم فنون تصفيف الشعر وعمل التسريحات المختلفة',
            'content' => 'دورة شاملة في تصفيف الشعر تشمل القص، التسريح، واستخدام أدوات التصفيف المختلفة للحصول على إطلالات متنوعة.',
            'price' => 700,
            'max_students' => 8,
            'duration_hours' => 35,
            'start_date' => '2024-02-15',
            'end_date' => '2024-03-10',
            'location' => 'صالون التدريب',
            'is_online' => false,
            'category' => 'دورات العناية بالشعر',
            'image' => 'courses/hairstyling.jpg'
        ],
        [
            'title' => 'دورة فن الأظافر والنيل آرت',
            'description' => 'تعلم تقنيات تجميل الأظافر والرسم عليها',
            'content' => 'دورة متخصصة في فن تجميل الأظافر تشمل المانيكير، البديكير، والرسم على الأظافر بتقنيات مختلفة.',
            'price' => 400,
            'max_students' => 10,
            'duration_hours' => 15,
            'start_date' => '2024-02-08',
            'end_date' => '2024-02-18',
            'location' => 'استوديو الأظافر',
            'is_online' => false,
            'category' => 'دورات الأظافر',
            'image' => 'courses/nail-art.jpg'
        ],
        [
            'title' => 'دورة المكياج الرقمي أونلاين',
            'description' => 'تعلم المكياج من المنزل عبر الإنترنت',
            'content' => 'دورة تفاعلية عبر الإنترنت تتيح لك تعلم المكياج من المنزل مع متابعة مباشرة من المدربين المختصين.',
            'price' => 350,
            'max_students' => 25,
            'duration_hours' => 18,
            'start_date' => '2024-02-12',
            'end_date' => '2024-02-25',
            'location' => null,
            'is_online' => true,
            'category' => 'دورات المكياج',
            'image' => 'courses/online-makeup.jpg'
        ]
    ];
    
    foreach ($beautyCourses as $courseData) {
        $category = $categories->get($courseData['category']);
        if (!$category) {
            echo "  ❌ لم يتم العثور على فئة الدورة: {$courseData['category']}\n";
            continue;
        }
        
        \Botble\TrainingAppointment\Models\Course::create([
            'title' => $courseData['title'],
            'description' => $courseData['description'],
            'content' => $courseData['content'],
            'price' => $courseData['price'],
            'max_students' => $courseData['max_students'],
            'enrolled_students' => rand(0, $courseData['max_students'] - 5),
            'duration_hours' => $courseData['duration_hours'],
            'start_date' => $courseData['start_date'],
            'end_date' => $courseData['end_date'],
            'location' => $courseData['location'],
            'is_online' => $courseData['is_online'],
            'status' => 'published',
            'category_id' => $category->id,
            'store_id' => null, // دورات من الأدمن
            'image' => $courseData['image']
        ]);
        
        echo "  ✅ تم إنشاء الدورة: {$courseData['title']}\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ خطأ في إنشاء الدورات: " . $e->getMessage() . "\n";
}

echo "\n✅ تم إنشاء جميع دورات العناية والجمال بنجاح!\n";
echo "\n📊 إحصائيات:\n";
echo "  - فئات الدورات: " . \Botble\TrainingAppointment\Models\CourseCategory::count() . "\n";
echo "  - إجمالي الدورات: " . \Botble\TrainingAppointment\Models\Course::count() . "\n";
echo "  - الدورات الحضورية: " . \Botble\TrainingAppointment\Models\Course::where('is_online', false)->count() . "\n";
echo "  - الدورات الأونلاين: " . \Botble\TrainingAppointment\Models\Course::where('is_online', true)->count() . "\n";

echo "\n🎉 تم الانتهاء من إعداد دورات العناية والجمال!\n";
