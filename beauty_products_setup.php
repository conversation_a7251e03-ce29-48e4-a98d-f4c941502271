<?php

echo "🛍️ إعداد منتجات العناية والجمال\n\n";

// إعداد Laravel
$autoload = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoload)) {
    require_once $autoload;
    
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} else {
    echo "❌ لا يمكن العثور على autoload.php\n";
    exit;
}

// 1. مسح المنتجات الحالية
echo "1. مسح المنتجات الحالية:\n";
try {
    $deletedProducts = \Botble\Ecommerce\Models\Product::count();
    \Botble\Ecommerce\Models\Product::truncate();
    \DB::table('ec_product_categories_products')->truncate();
    echo "  ✅ تم مسح {$deletedProducts} منتج\n";
} catch (Exception $e) {
    echo "  ❌ خطأ في مسح المنتجات: " . $e->getMessage() . "\n";
}

// 2. الحصول على الفئات
echo "\n2. الحصول على الفئات:\n";
$categories = \Botble\Ecommerce\Models\ProductCategory::all()->keyBy('name');
echo "  ✅ تم العثور على " . $categories->count() . " فئة\n";

// 3. إضافة منتجات العناية والجمال
echo "\n3. إضافة منتجات العناية والجمال:\n";

$beautyProducts = [
    // منتجات العناية بالبشرة
    [
        'name' => 'كريم مرطب للوجه بفيتامين C',
        'description' => 'كريم مرطب غني بفيتامين C لإشراق البشرة وترطيبها العميق',
        'content' => 'كريم مرطب فاخر يحتوي على فيتامين C الطبيعي، يساعد على ترطيب البشرة وإعطائها إشراقاً طبيعياً. مناسب لجميع أنواع البشرة.',
        'price' => 150,
        'sale_price' => 120,
        'sku' => 'FACE-CREAM-001',
        'category' => 'كريمات الوجه',
        'brand' => 'Beauty Pro',
        'image_url' => 'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=500&h=500&fit=crop'
    ],
    [
        'name' => 'سيروم الهيالورونيك أسيد',
        'description' => 'سيروم مركز بالهيالورونيك أسيد لترطيب البشرة ومحاربة علامات التقدم في السن',
        'content' => 'سيروم فعال يحتوي على الهيالورونيك أسيد بتركيز عالي، يساعد على ترطيب البشرة بعمق وتقليل ظهور التجاعيد.',
        'price' => 200,
        'sale_price' => 180,
        'sku' => 'SERUM-001',
        'category' => 'سيروم البشرة',
        'brand' => 'Glow Beauty',
        'image_url' => 'https://images.unsplash.com/photo-1620916566398-39f1143ab7be?w=500&h=500&fit=crop'
    ],
    [
        'name' => 'ماسك الطين المنقي',
        'description' => 'ماسك طبيعي بالطين لتنظيف المسام العميق وإزالة الشوائب',
        'content' => 'ماسك طبيعي 100% يحتوي على الطين البركاني، ينظف المسام بعمق ويزيل الشوائب والزيوت الزائدة.',
        'price' => 80,
        'sale_price' => 65,
        'sku' => 'MASK-001',
        'category' => 'ماسكات الوجه',
        'brand' => 'Natural Care',
        'image_url' => 'https://images.unsplash.com/photo-1596755389378-c31d21fd1273?w=500&h=500&fit=crop'
    ],
    
    // منتجات العناية بالشعر
    [
        'name' => 'شامبو الأرجان المغذي',
        'description' => 'شامبو طبيعي بزيت الأرجان لتغذية الشعر وإعطائه لمعاناً طبيعياً',
        'content' => 'شامبو فاخر يحتوي على زيت الأرجان المغربي الأصلي، ينظف الشعر بلطف ويغذيه من الجذور حتى الأطراف.',
        'price' => 120,
        'sale_price' => 100,
        'sku' => 'SHAMPOO-001',
        'category' => 'شامبو',
        'brand' => 'Argan Gold',
        'image_url' => 'https://images.unsplash.com/photo-1571781926291-c477ebfd024b?w=500&h=500&fit=crop'
    ],
    [
        'name' => 'بلسم الكيراتين المرمم',
        'description' => 'بلسم غني بالكيراتين لترميم الشعر التالف وحمايته من التقصف',
        'content' => 'بلسم علاجي يحتوي على الكيراتين الطبيعي، يرمم الشعر التالف ويحميه من العوامل الخارجية.',
        'price' => 140,
        'sale_price' => 115,
        'sku' => 'CONDITIONER-001',
        'category' => 'بلسم',
        'brand' => 'Keratin Pro',
        'image_url' => 'https://images.unsplash.com/photo-1522338242992-e1a54906a8da?w=500&h=500&fit=crop'
    ],
    
    // منتجات المكياج
    [
        'name' => 'كريم أساس طبيعي مقاوم للماء',
        'description' => 'كريم أساس بتغطية كاملة ومقاوم للماء لإطلالة مثالية طوال اليوم',
        'content' => 'كريم أساس فاخر بتركيبة طبيعية، يوفر تغطية كاملة ومقاوم للماء والعرق، مناسب لجميع أنواع البشرة.',
        'price' => 180,
        'sale_price' => 150,
        'sku' => 'FOUNDATION-001',
        'category' => 'كريم الأساس',
        'brand' => 'Makeup Pro',
        'image_url' => 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=500&h=500&fit=crop'
    ],
    [
        'name' => 'أحمر شفاه مات طويل الثبات',
        'description' => 'أحمر شفاه بملمس مات وثبات يدوم حتى 12 ساعة',
        'content' => 'أحمر شفاه فاخر بتركيبة مات، يوفر لوناً غنياً وثباتاً استثنائياً يدوم طوال اليوم دون تلطخ.',
        'price' => 90,
        'sale_price' => 75,
        'sku' => 'LIPSTICK-001',
        'category' => 'أحمر الشفاه',
        'brand' => 'Lip Perfect',
        'image_url' => 'https://images.unsplash.com/photo-1586495777744-4413f21062fa?w=500&h=500&fit=crop'
    ],
    
    // العطور
    [
        'name' => 'عطر زهري أنثوي فاخر',
        'description' => 'عطر نسائي بنفحات زهرية رومانسية وثبات استثنائي',
        'content' => 'عطر فاخر بتركيبة زهرية رقيقة، يجمع بين رائحة الورد والياسمين مع لمسة من الفانيليا، مثالي للمناسبات الخاصة.',
        'price' => 300,
        'sale_price' => 250,
        'sku' => 'PERFUME-001',
        'category' => 'عطور نسائية',
        'brand' => 'Luxury Scents',
        'image_url' => 'https://images.unsplash.com/photo-1541643600914-78b084683601?w=500&h=500&fit=crop'
    ],
    
    // العناية بالجسم
    [
        'name' => 'كريم الجسم بزبدة الشيا',
        'description' => 'كريم مرطب للجسم بزبدة الشيا الطبيعية لنعومة فائقة',
        'content' => 'كريم جسم فاخر يحتوي على زبدة الشيا الأفريقية الخالصة، يرطب البشرة بعمق ويتركها ناعمة ومشرقة.',
        'price' => 110,
        'sale_price' => 90,
        'sku' => 'BODY-CREAM-001',
        'category' => 'كريمات الجسم',
        'brand' => 'Shea Butter Co',
        'image_url' => 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=500&h=500&fit=crop'
    ]
];

foreach ($beautyProducts as $index => $productData) {
    try {
        // البحث عن الفئة
        $category = $categories->get($productData['category']);
        if (!$category) {
            echo "  ❌ لم يتم العثور على الفئة: {$productData['category']}\n";
            continue;
        }
        
        // إنشاء المنتج
        $product = \Botble\Ecommerce\Models\Product::create([
            'name' => $productData['name'],
            'description' => $productData['description'],
            'content' => $productData['content'],
            'price' => $productData['price'],
            'sale_price' => $productData['sale_price'],
            'sku' => $productData['sku'],
            'status' => 'published',
            'is_featured' => $index < 5, // أول 5 منتجات مميزة
            'brand_id' => null,
            'quantity' => rand(10, 100),
            'allow_checkout_when_out_of_stock' => false,
            'with_storehouse_management' => true,
            'stock_status' => 'in_stock',
            'weight' => rand(100, 500),
            'length' => rand(5, 15),
            'wide' => rand(5, 15),
            'height' => rand(5, 15),
            'image' => 'products/' . $productData['sku'] . '.jpg'
        ]);
        
        // ربط المنتج بالفئة
        $product->categories()->attach($category->id);
        
        echo "  ✅ تم إنشاء المنتج: {$productData['name']} (الفئة: {$productData['category']})\n";
        
    } catch (Exception $e) {
        echo "  ❌ خطأ في إنشاء المنتج {$productData['name']}: " . $e->getMessage() . "\n";
    }
}

echo "\n✅ تم إنشاء جميع منتجات العناية والجمال بنجاح!\n";
echo "\n📊 إحصائيات:\n";
echo "  - إجمالي المنتجات: " . \Botble\Ecommerce\Models\Product::count() . "\n";
echo "  - المنتجات المميزة: " . \Botble\Ecommerce\Models\Product::where('is_featured', true)->count() . "\n";

echo "\n🎉 تم الانتهاء من إعداد منتجات العناية والجمال!\n";
