<?php

echo "💅 إعداد خدمات العناية والجمال\n\n";

// إعداد Laravel
$autoload = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoload)) {
    require_once $autoload;
    
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} else {
    echo "❌ لا يمكن العثور على autoload.php\n";
    exit;
}

// 1. مسح الخدمات الحالية
echo "1. مسح الخدمات الحالية:\n";
try {
    $deletedServices = \Botble\TrainingAppointment\Models\Service::count();
    \Botble\TrainingAppointment\Models\Service::truncate();
    echo "  ✅ تم مسح {$deletedServices} خدمة\n";
} catch (Exception $e) {
    echo "  ❌ خطأ في مسح الخدمات: " . $e->getMessage() . "\n";
}

// 2. إضافة خدمات العناية والجمال
echo "\n2. إضافة خدمات العناية والجمال:\n";

$beautyServices = [
    [
        'name' => 'جلسة مكياج احترافي',
        'description' => 'جلسة مكياج احترافية للمناسبات الخاصة مع ميكب آرتيست محترف',
        'price' => 200,
        'duration' => 90,
        'is_featured' => true,
        'category' => 'مكياج'
    ],
    [
        'name' => 'مكياج العروس',
        'description' => 'مكياج خاص للعرائس يشمل التجربة والتطبيق يوم الزفاف',
        'price' => 500,
        'duration' => 120,
        'is_featured' => true,
        'category' => 'مكياج'
    ],
    [
        'name' => 'تنظيف البشرة العميق',
        'description' => 'جلسة تنظيف عميق للبشرة تشمل التقشير والترطيب',
        'price' => 150,
        'duration' => 60,
        'is_featured' => true,
        'category' => 'عناية بالبشرة'
    ],
    [
        'name' => 'فيشل للبشرة الحساسة',
        'description' => 'جلسة عناية خاصة للبشرة الحساسة بمنتجات طبيعية',
        'price' => 180,
        'duration' => 75,
        'is_featured' => false,
        'category' => 'عناية بالبشرة'
    ],
    [
        'name' => 'ماسك الذهب للوجه',
        'description' => 'ماسك فاخر بالذهب لإشراق البشرة ومحاربة علامات التقدم في السن',
        'price' => 300,
        'duration' => 90,
        'is_featured' => true,
        'category' => 'عناية بالبشرة'
    ],
    [
        'name' => 'قص وتصفيف الشعر',
        'description' => 'خدمة قص وتصفيف الشعر مع استشارة مجانية لاختيار القصة المناسبة',
        'price' => 120,
        'duration' => 60,
        'is_featured' => false,
        'category' => 'عناية بالشعر'
    ],
    [
        'name' => 'صبغة الشعر الطبيعية',
        'description' => 'صبغة شعر بمواد طبيعية آمنة مع العناية اللازمة',
        'price' => 250,
        'duration' => 120,
        'is_featured' => false,
        'category' => 'عناية بالشعر'
    ],
    [
        'name' => 'علاج الشعر بالكيراتين',
        'description' => 'علاج مكثف للشعر بالكيراتين لفرد وتنعيم الشعر',
        'price' => 400,
        'duration' => 180,
        'is_featured' => true,
        'category' => 'عناية بالشعر'
    ],
    [
        'name' => 'مانيكير وبديكير',
        'description' => 'جلسة عناية كاملة بالأظافر تشمل التنظيف والتلميع والطلاء',
        'price' => 80,
        'duration' => 45,
        'is_featured' => false,
        'category' => 'عناية بالأظافر'
    ],
    [
        'name' => 'نيل آرت احترافي',
        'description' => 'رسم احترافي على الأظافر بتصاميم مختلفة حسب الطلب',
        'price' => 120,
        'duration' => 60,
        'is_featured' => false,
        'category' => 'عناية بالأظافر'
    ],
    [
        'name' => 'تركيب الأظافر الجل',
        'description' => 'تركيب أظافر جل طبيعية المظهر مع إمكانية اختيار الطول والشكل',
        'price' => 150,
        'duration' => 90,
        'is_featured' => false,
        'category' => 'عناية بالأظافر'
    ],
    [
        'name' => 'جلسة تدليك الوجه',
        'description' => 'جلسة تدليك مريحة للوجه لتحفيز الدورة الدموية وإشراق البشرة',
        'price' => 100,
        'duration' => 45,
        'is_featured' => false,
        'category' => 'عناية بالبشرة'
    ],
    [
        'name' => 'إزالة الشعر بالليزر - الوجه',
        'description' => 'جلسة إزالة شعر الوجه بتقنية الليزر الآمنة',
        'price' => 200,
        'duration' => 30,
        'is_featured' => false,
        'category' => 'إزالة الشعر'
    ],
    [
        'name' => 'إزالة الشعر بالليزر - الجسم',
        'description' => 'جلسة إزالة شعر الجسم بتقنية الليزر المتطورة',
        'price' => 350,
        'duration' => 60,
        'is_featured' => false,
        'category' => 'إزالة الشعر'
    ],
    [
        'name' => 'استشارة جمالية شاملة',
        'description' => 'استشارة مع خبير تجميل لوضع روتين عناية مخصص',
        'price' => 100,
        'duration' => 30,
        'is_featured' => false,
        'category' => 'استشارات'
    ]
];

foreach ($beautyServices as $serviceData) {
    try {
        \Botble\TrainingAppointment\Models\Service::create([
            'name' => $serviceData['name'],
            'description' => $serviceData['description'],
            'price' => $serviceData['price'],
            'duration' => $serviceData['duration'],
            'status' => 'published',
            'is_featured' => $serviceData['is_featured'],
            'store_id' => null, // خدمات من الأدمن
            'category' => $serviceData['category']
        ]);
        
        echo "  ✅ تم إنشاء الخدمة: {$serviceData['name']} ({$serviceData['price']} ريال - {$serviceData['duration']} دقيقة)\n";
        
    } catch (Exception $e) {
        echo "  ❌ خطأ في إنشاء الخدمة {$serviceData['name']}: " . $e->getMessage() . "\n";
    }
}

echo "\n✅ تم إنشاء جميع خدمات العناية والجمال بنجاح!\n";
echo "\n📊 إحصائيات:\n";
echo "  - إجمالي الخدمات: " . \Botble\TrainingAppointment\Models\Service::count() . "\n";
echo "  - الخدمات المميزة: " . \Botble\TrainingAppointment\Models\Service::where('is_featured', true)->count() . "\n";

// إحصائيات حسب الفئة
$categories = \Botble\TrainingAppointment\Models\Service::select('category')
    ->groupBy('category')
    ->get()
    ->pluck('category');

echo "  - الفئات المتاحة:\n";
foreach ($categories as $category) {
    $count = \Botble\TrainingAppointment\Models\Service::where('category', $category)->count();
    echo "    • {$category}: {$count} خدمة\n";
}

echo "\n🎉 تم الانتهاء من إعداد خدمات العناية والجمال!\n";
