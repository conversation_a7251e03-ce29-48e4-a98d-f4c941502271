<?php

echo "📅 اختبار إصلاح مشكلة حجز المواعيد\n\n";

// 1. فحص Routes
echo "1. فحص Routes:\n";
$output = shell_exec('php artisan route:list --name="book-appointment" 2>&1');
if ($output) {
    echo "  ✅ Routes موجودة:\n";
    echo "    " . trim($output) . "\n";
} else {
    echo "  ❌ لا يمكن فحص Routes\n";
}

// 2. فحص PublicAppointmentController
echo "\n2. فحص PublicAppointmentController:\n";
$controllerFile = 'platform/plugins/training-appointment/src/Http/Controllers/PublicAppointmentController.php';
if (file_exists($controllerFile)) {
    $content = file_get_contents($controllerFile);
    
    $checks = [
        'public function bookAppointment($storeId' => 'تصحيح parameter name',
        "where('id', \$storeId)" => 'استخدام ID بدلاً من slug',
        'firstOrFail()' => 'التعامل مع المتجر غير الموجود',
    ];
    
    foreach ($checks as $search => $description) {
        if (strpos($content, $search) !== false) {
            echo "  ✅ $description: موجود\n";
        } else {
            echo "  ❌ $description: مفقود\n";
        }
    }
} else {
    echo "  ❌ PublicAppointmentController: مفقود\n";
}

// 3. فحص View
echo "\n3. فحص View:\n";
$viewFile = 'platform/plugins/training-appointment/resources/views/themes/book-appointment.blade.php';
if (file_exists($viewFile)) {
    $content = file_get_contents($viewFile);
    
    $checks = [
        'route(\'public.store.store-appointment\', $store->id)' => 'تصحيح route في form action',
        'id="service_id"' => 'حقل اختيار الخدمة',
        'id="appointment_date"' => 'حقل اختيار التاريخ',
        'id="time-slots"' => 'منطقة عرض الأوقات',
        'loadAvailableSlots()' => 'دالة تحميل الأوقات',
        'route(\'public.ajax.available-slots\')' => 'AJAX route للأوقات',
    ];
    
    foreach ($checks as $search => $description) {
        if (strpos($content, $search) !== false) {
            echo "  ✅ $description: موجود\n";
        } else {
            echo "  ❌ $description: مفقود\n";
        }
    }
} else {
    echo "  ❌ View file: مفقود\n";
}

// 4. فحص AJAX Route
echo "\n4. فحص AJAX Route:\n";
$ajaxOutput = shell_exec('php artisan route:list --name="available-slots" 2>&1');
if ($ajaxOutput && strpos($ajaxOutput, 'available-slots') !== false) {
    echo "  ✅ AJAX Route للأوقات المتاحة: موجود\n";
} else {
    echo "  ❌ AJAX Route للأوقات المتاحة: مفقود\n";
}

// 5. فحص الرابط في صفحة المتجر
echo "\n5. فحص الرابط في صفحة المتجر:\n";
$storeViewFile = 'platform/themes/farmart/views/marketplace/store.blade.php';
if (file_exists($storeViewFile)) {
    $content = file_get_contents($storeViewFile);
    
    if (strpos($content, 'route(\'public.store.book-appointment\', $store->id)') !== false) {
        echo "  ✅ رابط حجز الموعد: صحيح\n";
    } else {
        echo "  ❌ رابط حجز الموعد: خطأ\n";
    }
    
    if (strpos($content, 'احجز موعد') !== false) {
        echo "  ✅ نص الزر: موجود\n";
    } else {
        echo "  ❌ نص الزر: مفقود\n";
    }
} else {
    echo "  ❌ صفحة المتجر: مفقودة\n";
}

// 6. فحص Models
echo "\n6. فحص Models:\n";
$models = [
    'platform/plugins/training-appointment/src/Models/Service.php' => 'Service Model',
    'platform/plugins/training-appointment/src/Models/Appointment.php' => 'Appointment Model',
    'platform/plugins/marketplace/src/Models/Store.php' => 'Store Model',
];

foreach ($models as $file => $description) {
    if (file_exists($file)) {
        echo "  ✅ $description: موجود\n";
    } else {
        echo "  ❌ $description: مفقود\n";
    }
}

// 7. فحص Request Validation
echo "\n7. فحص Request Validation:\n";
$requestFile = 'platform/plugins/training-appointment/src/Http/Requests/BookAppointmentRequest.php';
if (file_exists($requestFile)) {
    echo "  ✅ BookAppointmentRequest: موجود\n";
    
    $content = file_get_contents($requestFile);
    $validations = [
        'service_id' => 'تحقق من الخدمة',
        'appointment_date' => 'تحقق من التاريخ',
        'start_time' => 'تحقق من الوقت',
    ];
    
    foreach ($validations as $field => $description) {
        if (strpos($content, "'$field'") !== false) {
            echo "    ✅ $description: موجود\n";
        } else {
            echo "    ❌ $description: مفقود\n";
        }
    }
} else {
    echo "  ❌ BookAppointmentRequest: مفقود\n";
}

echo "\n🎯 النتيجة:\n";
echo "إذا كانت جميع العناصر أعلاه تظهر ✅، فمشكلة حجز المواعيد تم حلها!\n\n";

echo "🔧 ما تم إصلاحه:\n";
echo "1. ✅ تصحيح parameter في PublicAppointmentController\n";
echo "2. ✅ استخدام Store ID بدلاً من slug\n";
echo "3. ✅ تصحيح route في form action\n";
echo "4. ✅ التأكد من وجود جميع المكونات المطلوبة\n\n";

echo "🚀 للاختبار:\n";
echo "1. اذهب لصفحة أي فيندور\n";
echo "2. اضغط على تبويب 'الخدمات'\n";
echo "3. اضغط على 'احجز موعد' لأي خدمة\n";
echo "4. يجب أن تفتح صفحة حجز الموعد بدون خطأ 404\n";
echo "5. اختر الخدمة والتاريخ والوقت\n";
echo "6. اضغط 'تأكيد الحجز'\n\n";

echo "📋 مميزات صفحة حجز الموعد:\n";
echo "- ✅ اختيار الخدمة مع عرض السعر والمدة\n";
echo "- ✅ اختيار التاريخ (30 يوم قادم)\n";
echo "- ✅ عرض الأوقات المتاحة تلقائياً\n";
echo "- ✅ منع حجز الأوقات المحجوزة مسبقاً\n";
echo "- ✅ ساعات عمل افتراضية (9 صباحاً - 6 مساءً)\n";
echo "- ✅ فترة استراحة (12-1 ظهراً)\n";
echo "- ✅ إضافة ملاحظات اختيارية\n\n";

echo "🎉 إذا نجح الاختبار، فنظام حجز المواعيد يعمل بشكل مثالي!\n";
