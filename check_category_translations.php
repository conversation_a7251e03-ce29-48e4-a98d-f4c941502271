<?php

echo "🔍 فحص ترجمات فئات المنتجات\n\n";

// إعداد Laravel
$autoload = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoload)) {
    require_once $autoload;
    
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} else {
    echo "❌ لا يمكن العثور على autoload.php\n";
    exit;
}

echo "📊 1. فحص فئات المنتجات الحالية:\n";
echo "==================================\n";

try {
    $categories = DB::table('ec_product_categories')
        ->where('status', 'published')
        ->orderBy('order')
        ->get();
    
    echo "📋 إجمالي الفئات: " . $categories->count() . "\n\n";
    
    foreach ($categories as $category) {
        echo "📌 فئة #{$category->id}: {$category->name}\n";
        echo "   الترتيب: {$category->order}\n";
        echo "   الحالة: {$category->status}\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في جلب الفئات: " . $e->getMessage() . "\n\n";
}

echo "🌐 2. فحص جدول الترجمات:\n";
echo "=========================\n";

try {
    // فحص وجود جدول الترجمات
    $hasTranslationsTable = Schema::hasTable('ec_product_categories_translations');
    echo "📋 جدول الترجمات موجود: " . ($hasTranslationsTable ? 'نعم' : 'لا') . "\n";
    
    if ($hasTranslationsTable) {
        $translations = DB::table('ec_product_categories_translations')->get();
        echo "📊 إجمالي الترجمات: " . $translations->count() . "\n\n";
        
        if ($translations->count() > 0) {
            echo "📝 الترجمات الموجودة:\n";
            foreach ($translations as $translation) {
                echo "  🔤 فئة #{$translation->ec_product_categories_id} - {$translation->lang_code}: {$translation->name}\n";
            }
        } else {
            echo "⚠️ لا توجد ترجمات في الجدول\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في فحص الترجمات: " . $e->getMessage() . "\n\n";
}

echo "\n🔧 3. فحص إعدادات اللغات:\n";
echo "==========================\n";

try {
    $languages = DB::table('languages')
        ->where('lang_is_default', 0)
        ->orWhere('lang_is_default', 1)
        ->get();
    
    echo "🌍 اللغات المتاحة:\n";
    foreach ($languages as $language) {
        $isDefault = $language->lang_is_default ? ' (افتراضية)' : '';
        echo "  🔤 {$language->lang_name} ({$language->lang_code}){$isDefault}\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في فحص اللغات: " . $e->getMessage() . "\n\n";
}

echo "\n🧪 4. اختبار عرض الفئات بلغات مختلفة:\n";
echo "========================================\n";

try {
    // اختبار العربية
    app()->setLocale('ar');
    echo "🇸🇦 **العربية:**\n";
    
    $categoriesAr = \Botble\Ecommerce\Models\ProductCategory::where('status', 'published')
        ->orderBy('order')
        ->limit(5)
        ->get();
    
    foreach ($categoriesAr as $category) {
        echo "  📌 {$category->name}\n";
    }
    
    // اختبار الإنجليزية
    app()->setLocale('en');
    echo "\n🇺🇸 **الإنجليزية:**\n";
    
    $categoriesEn = \Botble\Ecommerce\Models\ProductCategory::where('status', 'published')
        ->orderBy('order')
        ->limit(5)
        ->get();
    
    foreach ($categoriesEn as $category) {
        echo "  📌 {$category->name}\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار العرض: " . $e->getMessage() . "\n\n";
}

echo "\n💡 5. اقتراحات الحل:\n";
echo "===================\n";

$hasTranslationsTable = Schema::hasTable('ec_product_categories_translations');
$translationsCount = $hasTranslationsTable ? DB::table('ec_product_categories_translations')->count() : 0;

if (!$hasTranslationsTable) {
    echo "❌ **المشكلة**: جدول الترجمات غير موجود\n";
    echo "🔧 **الحل**: تشغيل migration لإنشاء الجدول\n\n";
} elseif ($translationsCount == 0) {
    echo "❌ **المشكلة**: جدول الترجمات فارغ\n";
    echo "🔧 **الحل**: إضافة ترجمات للفئات الموجودة\n\n";
} else {
    echo "✅ **الوضع**: جدول الترجمات موجود ويحتوي على بيانات\n";
    echo "🔧 **قد تحتاج**: إضافة ترجمات مفقودة أو تحديث الموجودة\n\n";
}

echo "📋 **خطوات الحل المقترحة:**\n";
echo "1. التأكد من وجود جدول الترجمات\n";
echo "2. إضافة ترجمات إنجليزية للفئات العربية\n";
echo "3. تحديث ProductCategoryHelper للتأكد من استخدام الترجمات\n";
echo "4. مسح الـ cache\n\n";

echo "✅ تم الانتهاء من الفحص\n";
