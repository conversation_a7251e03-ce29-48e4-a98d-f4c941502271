<?php

require_once 'vendor/autoload.php';

use Botble\TrainingAppointment\Models\Course;
use Botble\TrainingAppointment\Models\CourseCategory;

// التحقق من أنك في بيئة Laravel
if (!function_exists('app')) {
    die("This script must be run in Laravel environment.\n");
}

echo "🔍 فحص الدورات والفئات...\n\n";

// فحص الفئات
echo "=== الفئات المتاحة ===\n";
$categories = CourseCategory::all();
foreach ($categories as $category) {
    echo "ID: {$category->id} | الاسم: {$category->name} | الحالة: {$category->status}\n";
}

echo "\n=== الدورات المتاحة ===\n";
$courses = Course::with('category')->get();
foreach ($courses as $course) {
    $categoryName = $course->category ? $course->category->name : 'بدون فئة';
    echo "ID: {$course->id} | العنوان: {$course->title} | الفئة: {$categoryName} | الحالة: {$course->status}\n";
}

echo "\n=== الدورات المنشورة فقط ===\n";
$publishedCourses = Course::where('status', 'published')->with('category')->get();
echo "عدد الدورات المنشورة: " . $publishedCourses->count() . "\n";
foreach ($publishedCourses as $course) {
    $categoryName = $course->category ? $course->category->name : 'بدون فئة';
    echo "- {$course->title} (الفئة: {$categoryName})\n";
}

echo "\n=== فحص الفئات المنشورة ===\n";
$publishedCategories = CourseCategory::where('status', 'published')->get();
echo "عدد الفئات المنشورة: " . $publishedCategories->count() . "\n";
foreach ($publishedCategories as $category) {
    $coursesCount = Course::where('category_id', $category->id)->where('status', 'published')->count();
    echo "- {$category->name} ({$coursesCount} دورة)\n";
}

echo "\nانتهى الفحص!\n";
