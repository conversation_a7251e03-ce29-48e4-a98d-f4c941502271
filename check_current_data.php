<?php

require_once 'bootstrap/app.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 فحص البيانات الحالية في النظام\n\n";

// 1. فحص الفئات الحالية
echo "1. الفئات الحالية:\n";
try {
    $categories = \Botble\Ecommerce\Models\ProductCategory::all(['id', 'name', 'parent_id']);
    if ($categories->count() > 0) {
        foreach ($categories as $category) {
            $parent = $category->parent_id ? " (Parent: {$category->parent_id})" : " (Root)";
            echo "  📁 {$category->id} - {$category->name}{$parent}\n";
        }
    } else {
        echo "  ❌ لا توجد فئات\n";
    }
} catch (Exception $e) {
    echo "  ❌ خطأ في فحص الفئات: " . $e->getMessage() . "\n";
}

// 2. فحص المنتجات الحالية
echo "\n2. المنتجات الحالية:\n";
try {
    $products = \Botble\Ecommerce\Models\Product::with('categories')->take(10)->get(['id', 'name', 'price']);
    if ($products->count() > 0) {
        foreach ($products as $product) {
            $categories = $product->categories->pluck('name')->join(', ');
            echo "  📦 {$product->id} - {$product->name} - {$product->price} ريال";
            if ($categories) {
                echo " (الفئات: {$categories})";
            }
            echo "\n";
        }
        
        $totalProducts = \Botble\Ecommerce\Models\Product::count();
        echo "  📊 إجمالي المنتجات: {$totalProducts}\n";
    } else {
        echo "  ❌ لا توجد منتجات\n";
    }
} catch (Exception $e) {
    echo "  ❌ خطأ في فحص المنتجات: " . $e->getMessage() . "\n";
}

// 3. فحص فئات الدورات
echo "\n3. فئات الدورات:\n";
try {
    $courseCategories = \Botble\TrainingAppointment\Models\CourseCategory::all(['id', 'name']);
    if ($courseCategories->count() > 0) {
        foreach ($courseCategories as $category) {
            echo "  🎓 {$category->id} - {$category->name}\n";
        }
    } else {
        echo "  ❌ لا توجد فئات دورات\n";
    }
} catch (Exception $e) {
    echo "  ❌ خطأ في فحص فئات الدورات: " . $e->getMessage() . "\n";
}

// 4. فحص الدورات الحالية
echo "\n4. الدورات الحالية:\n";
try {
    $courses = \Botble\TrainingAppointment\Models\Course::with('category')->take(5)->get(['id', 'title', 'price', 'category_id']);
    if ($courses->count() > 0) {
        foreach ($courses as $course) {
            $category = $course->category ? $course->category->name : 'بدون فئة';
            echo "  🎓 {$course->id} - {$course->title} - {$course->price} ريال (الفئة: {$category})\n";
        }
        
        $totalCourses = \Botble\TrainingAppointment\Models\Course::count();
        echo "  📊 إجمالي الدورات: {$totalCourses}\n";
    } else {
        echo "  ❌ لا توجد دورات\n";
    }
} catch (Exception $e) {
    echo "  ❌ خطأ في فحص الدورات: " . $e->getMessage() . "\n";
}

echo "\n✅ انتهى فحص البيانات الحالية\n";
