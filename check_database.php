<?php

require_once 'bootstrap/app.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== فحص قاعدة البيانات ===\n";

// فحص الجداول
echo "فحص الجداول:\n";
$tables = ['ta_courses', 'ta_services', 'ta_appointments', 'mp_stores'];
foreach ($tables as $table) {
    if (Schema::hasTable($table)) {
        $count = DB::table($table)->count();
        echo "✅ $table: $count سجل\n";
    } else {
        echo "❌ $table: غير موجود\n";
    }
}

// فحص الأعمدة المطلوبة
echo "\nفحص الأعمدة:\n";
if (Schema::hasTable('ta_courses')) {
    $columns = ['store_id', 'title', 'price', 'status'];
    foreach ($columns as $column) {
        if (Schema::hasColumn('ta_courses', $column)) {
            echo "✅ ta_courses.$column: موجود\n";
        } else {
            echo "❌ ta_courses.$column: مفقود\n";
        }
    }
}

// فحص البيانات
echo "\nفحص البيانات:\n";
if (Schema::hasTable('ta_courses')) {
    $courses = DB::table('ta_courses')->select('id', 'title', 'store_id', 'status')->limit(5)->get();
    foreach ($courses as $course) {
        echo "📚 دورة: $course->title (متجر: $course->store_id, حالة: $course->status)\n";
    }
}

if (Schema::hasTable('mp_stores')) {
    $stores = DB::table('mp_stores')->select('id', 'name', 'is_approved')->limit(3)->get();
    foreach ($stores as $store) {
        echo "🏪 متجر: $store->name (معرف: $store->id, معتمد: $store->is_approved)\n";
    }
}

// فحص المستخدمين
echo "\nفحص المستخدمين:\n";
if (Schema::hasTable('ec_customers')) {
    $customers = DB::table('ec_customers')
        ->join('mp_stores', 'ec_customers.id', '=', 'mp_stores.customer_id')
        ->select('ec_customers.name', 'ec_customers.email', 'mp_stores.name as store_name', 'mp_stores.id as store_id')
        ->limit(3)
        ->get();
    
    foreach ($customers as $customer) {
        echo "👤 عميل: $customer->name ($customer->email) - متجر: $customer->store_name (معرف: $customer->store_id)\n";
    }
}

echo "\n=== انتهى الفحص ===\n";
