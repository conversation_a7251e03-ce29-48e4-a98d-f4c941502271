<?php

echo "🔍 فحص إعدادات اللغة والـ URL\n\n";

// إعداد Laravel
$autoload = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoload)) {
    require_once $autoload;
    
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} else {
    echo "❌ لا يمكن العثور على autoload.php\n";
    exit;
}

echo "📋 اللغات المتاحة:\n";
echo "==================\n";

try {
    $languages = \Botble\Language\Models\Language::all();
    foreach ($languages as $lang) {
        $default = $lang->lang_is_default ? ' (افتراضية)' : '';
        echo "  • {$lang->lang_name} ({$lang->lang_locale}){$default}\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص اللغات: " . $e->getMessage() . "\n";
}

echo "\n⚙️ إعدادات اللغة:\n";
echo "=================\n";

try {
    $hideDefault = setting('language_hide_default');
    echo "  • إخفاء اللغة الافتراضية: " . ($hideDefault ? 'نعم' : 'لا') . "\n";
    
    $defaultLocale = \Botble\Language\Facades\Language::getDefaultLocale();
    echo "  • اللغة الافتراضية: $defaultLocale\n";
    
    $currentLocale = app()->getLocale();
    echo "  • اللغة الحالية: $currentLocale\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في فحص الإعدادات: " . $e->getMessage() . "\n";
}

echo "\n🔗 اختبار URLs:\n";
echo "===============\n";

try {
    $baseUrl = route('marketplace.vendor.services.index');
    echo "  ✅ Vendor Services (بدون prefix): $baseUrl\n";
} catch (Exception $e) {
    echo "  ❌ خطأ في route بدون prefix: " . $e->getMessage() . "\n";
}

try {
    $arabicUrl = \Botble\Language\Facades\Language::getLocalizedURL('ar', route('marketplace.vendor.services.index'));
    echo "  ✅ Vendor Services (عربي): $arabicUrl\n";
} catch (Exception $e) {
    echo "  ❌ خطأ في route عربي: " . $e->getMessage() . "\n";
}

try {
    $englishUrl = \Botble\Language\Facades\Language::getLocalizedURL('en', route('marketplace.vendor.services.index'));
    echo "  ✅ Vendor Services (إنجليزي): $englishUrl\n";
} catch (Exception $e) {
    echo "  ❌ خطأ في route إنجليزي: " . $e->getMessage() . "\n";
}

echo "\n🔧 تحليل المشكلة:\n";
echo "=================\n";

$hideDefault = setting('language_hide_default');
$defaultLocale = \Botble\Language\Facades\Language::getDefaultLocale();

echo "المشكلة الحالية:\n";
echo "  • اللغة الافتراضية ($defaultLocale) مخفية من الـ URL\n";
echo "  • عند التبديل للعربية يظهر /ar/\n";
echo "  • عند العودة للإنجليزية يظهر /en/ بدلاً من إزالة الـ prefix\n\n";

echo "الحل المطلوب:\n";
echo "  1. التأكد من أن الإنجليزية هي اللغة الافتراضية\n";
echo "  2. إعداد النظام لإزالة /en/ من الـ URL للغة الافتراضية\n";
echo "  3. إعادة توجيه /en/ إلى الـ URL بدون prefix\n\n";

echo "🛠️ الحلول المقترحة:\n";
echo "=====================\n";

echo "الحل 1: تحديث إعدادات اللغة\n";
echo "الحل 2: إضافة middleware للتعامل مع /en/\n";
echo "الحل 3: تحديث routes للتعامل مع المشكلة\n\n";

echo "📊 تفاصيل إضافية:\n";
echo "==================\n";

try {
    $supportedLocales = \Botble\Language\Facades\Language::getSupportedLocales();
    echo "اللغات المدعومة:\n";
    foreach ($supportedLocales as $locale => $data) {
        echo "  • $locale: {$data['name']}\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص اللغات المدعومة: " . $e->getMessage() . "\n";
}

echo "\n✅ تم الانتهاء من فحص إعدادات اللغة!\n";
