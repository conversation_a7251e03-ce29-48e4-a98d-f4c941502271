<?php

/**
 * فحص تسجيل القوائم في vendor dashboard
 */

echo "🔍 فحص تسجيل القوائم في vendor dashboard...\n\n";

// 1. فحص ملف TrainingAppointmentServiceProvider
echo "📋 فحص TrainingAppointmentServiceProvider:\n";
echo str_repeat("=", 60) . "\n";

$serviceProviderFile = 'platform/plugins/training-appointment/src/Providers/TrainingAppointmentServiceProvider.php';
if (file_exists(__DIR__ . '/' . $serviceProviderFile)) {
    echo "✅ ملف TrainingAppointmentServiceProvider موجود\n";
    
    $content = file_get_contents(__DIR__ . '/' . $serviceProviderFile);
    
    // البحث عن تسجيل القوائم
    if (preg_match('/DashboardMenu::for\(\'vendor\'\)->beforeRetrieving\(function \(\) \{(.*?)\}\);/s', $content, $matches)) {
        echo "✅ وجدت تسجيل قائمة البائع\n";
        
        // استخراج عناصر القائمة
        if (preg_match_all('/->registerItem\(\[(.*?)\]\)/s', $matches[1], $itemMatches)) {
            echo "📌 عناصر القائمة المسجلة:\n";
            
            foreach ($itemMatches[1] as $index => $itemContent) {
                // استخراج المعلومات
                preg_match("/'id' => '([^']+)'/", $itemContent, $idMatch);
                preg_match("/'name' => '([^']+)'/", $itemContent, $nameMatch);
                preg_match("/'url' => fn \(\) => route\('([^']+)'\)/", $itemContent, $urlMatch);
                preg_match("/'icon' => '([^']+)'/", $itemContent, $iconMatch);
                preg_match("/'priority' => (\d+)/", $itemContent, $priorityMatch);
                
                $id = $idMatch[1] ?? 'غير محدد';
                $name = $nameMatch[1] ?? 'غير محدد';
                $route = $urlMatch[1] ?? 'غير محدد';
                $icon = $iconMatch[1] ?? 'غير محدد';
                $priority = $priorityMatch[1] ?? 'غير محدد';
                
                echo "  " . ($index + 1) . ". {$name}\n";
                echo "     🆔 المعرف: {$id}\n";
                echo "     🛣️ المسار: {$route}\n";
                echo "     🎨 الأيقونة: {$icon}\n";
                echo "     📊 الأولوية: {$priority}\n\n";
            }
        }
    } else {
        echo "❌ لم أجد تسجيل قائمة البائع\n";
    }
    
    // فحص استدعاء الدالة
    if (strpos($content, '$this->registerVendorDashboardMenus();') !== false) {
        echo "✅ يتم استدعاء دالة تسجيل القوائم\n";
    } else {
        echo "❌ لا يتم استدعاء دالة تسجيل القوائم\n";
    }
    
} else {
    echo "❌ ملف TrainingAppointmentServiceProvider مفقود\n";
}

// 2. فحص ملف MarketplaceServiceProvider للتأكد من عدم وجود تضارب
echo "\n📋 فحص MarketplaceServiceProvider:\n";
echo str_repeat("=", 60) . "\n";

$marketplaceServiceProvider = 'platform/plugins/marketplace/src/Providers/MarketplaceServiceProvider.php';
if (file_exists(__DIR__ . '/' . $marketplaceServiceProvider)) {
    $content = file_get_contents(__DIR__ . '/' . $marketplaceServiceProvider);
    
    $trainingItems = [
        'marketplace.vendor.courses',
        'marketplace.vendor.lessons', 
        'marketplace.vendor.services',
        'marketplace.vendor.appointments',
    ];
    
    $hasConflict = false;
    foreach ($trainingItems as $item) {
        if (strpos($content, $item) !== false) {
            echo "⚠️ تضارب: {$item} مسجل في MarketplaceServiceProvider\n";
            $hasConflict = true;
        }
    }
    
    if (!$hasConflict) {
        echo "✅ لا يوجد تضارب في MarketplaceServiceProvider\n";
    }
} else {
    echo "❌ ملف MarketplaceServiceProvider مفقود\n";
}

// 3. فحص المسارات في fronts.php
echo "\n🛣️ فحص المسارات في fronts.php:\n";
echo str_repeat("=", 60) . "\n";

$routesFile = 'platform/plugins/marketplace/routes/fronts.php';
if (file_exists(__DIR__ . '/' . $routesFile)) {
    $content = file_get_contents(__DIR__ . '/' . $routesFile);
    
    $expectedRoutes = [
        'courses' => 'الدورات',
        'lessons' => 'الدروس',
        'services' => 'الخدمات',
        'appointments' => 'المواعيد',
    ];
    
    foreach ($expectedRoutes as $route => $description) {
        if (strpos($content, "['prefix' => '{$route}', 'as' => '{$route}.']") !== false) {
            echo "✅ مجموعة مسارات {$description}: مسجلة\n";
            
            // فحص المسارات الفرعية
            $pattern = "/Route::group\(\['prefix' => '{$route}', 'as' => '{$route}\.'\], function \(\) \{(.*?)\}\);/s";
            if (preg_match($pattern, $content, $matches)) {
                $routeContent = $matches[1];
                
                $subRoutes = ['index', 'create', 'store', 'show', 'edit', 'update', 'destroy'];
                foreach ($subRoutes as $subRoute) {
                    if (strpos($routeContent, "'as' => '{$subRoute}'") !== false) {
                        echo "  ✅ {$route}.{$subRoute}: مسجل\n";
                    }
                }
            }
        } else {
            echo "❌ مجموعة مسارات {$description}: غير مسجلة\n";
        }
    }
} else {
    echo "❌ ملف المسارات مفقود\n";
}

// 4. فحص ملف القائمة
echo "\n📄 فحص ملف القائمة:\n";
echo str_repeat("=", 60) . "\n";

$menuFiles = [
    'platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/menu.blade.php',
    'resources/views/vendor/plugins/marketplace/themes/vendor-dashboard/layouts/menu.blade.php',
];

$menuFileFound = false;
foreach ($menuFiles as $file) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "✅ ملف القائمة موجود: {$file}\n";
        $menuFileFound = true;
        
        $content = file_get_contents(__DIR__ . '/' . $file);
        
        if (strpos($content, "DashboardMenu::getAll('vendor')") !== false) {
            echo "✅ الملف يستخدم قائمة البائع بشكل صحيح\n";
        } else {
            echo "⚠️ الملف قد لا يستخدم قائمة البائع بشكل صحيح\n";
        }
        
        echo "📝 محتوى ملف القائمة:\n";
        echo "```blade\n";
        echo $content;
        echo "\n```\n";
        break;
    }
}

if (!$menuFileFound) {
    echo "❌ لم أجد أي ملف قائمة\n";
}

// 5. التشخيص والحلول
echo "\n💡 التشخيص والحلول:\n";
echo str_repeat("=", 60) . "\n";

echo "🔧 خطوات الإصلاح الموصى بها:\n";
echo "1. امسح الكاش: php artisan route:clear && php artisan cache:clear\n";
echo "2. تأكد من أن إضافة training-appointment مفعلة\n";
echo "3. تأكد من أن إضافة marketplace مفعلة\n";
echo "4. تحقق من أن المسارات مسجلة بشكل صحيح\n";
echo "5. تحقق من أن القوائم مسجلة بدون تضارب\n";

echo "\n🏁 انتهى فحص تسجيل القوائم!\n";
