<?php
// ملف فحص سريع للتأكد من إعدادات الاستضافة

echo "<h2>فحص إعدادات الاستضافة</h2>";

// فحص إصدار PHP
echo "<p><strong>إصدار PHP:</strong> " . phpversion() . "</p>";

// فحص Extensions المطلوبة
$required_extensions = ['openssl', 'pdo', 'mbstring', 'tokenizer', 'xml', 'ctype', 'json', 'bcmath', 'gd'];
echo "<h3>Extensions المطلوبة:</h3><ul>";
foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✅' : '❌';
    echo "<li>$ext: $status</li>";
}
echo "</ul>";

// فحص الملفات المهمة
$important_files = [
    'public/vendor/core/core/base/libraries/jquery.min.js',
    'public/vendor/core/core/base/libraries/font-awesome/css/fontawesome.min.css',
    'public/vendor/core/core/base/css/core.css',
    'public/vendor/core/core/base/js/app.js'
];

echo "<h3>الملفات المهمة:</h3><ul>";
foreach ($important_files as $file) {
    $status = file_exists($file) ? '✅' : '❌';
    echo "<li>$file: $status</li>";
}
echo "</ul>";

// فحص صلاحيات المجلدات
$folders = ['storage', 'bootstrap/cache', 'public'];
echo "<h3>صلاحيات المجلدات:</h3><ul>";
foreach ($folders as $folder) {
    $writable = is_writable($folder) ? '✅' : '❌';
    echo "<li>$folder: $writable</li>";
}
echo "</ul>";

// فحص متغيرات البيئة
echo "<h3>متغيرات البيئة:</h3>";
echo "<p><strong>APP_ENV:</strong> " . (getenv('APP_ENV') ?: 'غير محدد') . "</p>";
echo "<p><strong>APP_DEBUG:</strong> " . (getenv('APP_DEBUG') ?: 'غير محدد') . "</p>";
echo "<p><strong>DB_CONNECTION:</strong> " . (getenv('DB_CONNECTION') ?: 'غير محدد') . "</p>";

echo "<hr><p><strong>ملاحظة:</strong> احذف هذا الملف بعد التحقق من الإعدادات!</p>";
?>