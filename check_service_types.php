<?php

/**
 * التحقق من أنواع الخدمات في قاعدة البيانات
 */

require_once __DIR__ . '/vendor/autoload.php';

// تهيئة Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "🔍 التحقق من أنواع الخدمات في قاعدة البيانات...\n\n";

try {
    // التحقق من وجود الجدول
    if (!DB::getSchemaBuilder()->hasTable('mp_service_types')) {
        echo "❌ جدول mp_service_types غير موجود.\n";
        exit(1);
    }

    // الحصول على البيانات الموجودة
    $serviceTypes = DB::table('mp_service_types')->get();

    if ($serviceTypes->isEmpty()) {
        echo "⚠️ لا توجد أنواع خدمات في قاعدة البيانات.\n";
        echo "سيتم إدراج البيانات الافتراضية...\n\n";

        // إدراج البيانات الافتراضية
        $defaultTypes = [
            [
                'name' => 'المنتجات',
                'slug' => 'products',
                'description' => 'عمولة بيع المنتجات العادية',
                'default_commission_rate' => 3.00,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'الدورات التدريبية',
                'slug' => 'courses',
                'description' => 'عمولة الدورات التدريبية والتعليمية',
                'default_commission_rate' => 5.00,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'الخدمات',
                'slug' => 'services',
                'description' => 'عمولة الخدمات المختلفة',
                'default_commission_rate' => 4.00,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'المواعيد والحجوزات',
                'slug' => 'appointments',
                'description' => 'عمولة حجز المواعيد',
                'default_commission_rate' => 2.50,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'الاستشارات',
                'slug' => 'consultations',
                'description' => 'عمولة الاستشارات المختلفة',
                'default_commission_rate' => 6.00,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($defaultTypes as $type) {
            DB::table('mp_service_types')->insert($type);
            echo "✅ تم إدراج: {$type['name']} ({$type['default_commission_rate']}%)\n";
        }

        echo "\n🎉 تم إدراج البيانات الافتراضية بنجاح!\n";

    } else {
        echo "✅ يوجد {$serviceTypes->count()} نوع خدمة في قاعدة البيانات:\n\n";

        foreach ($serviceTypes as $type) {
            $status = $type->is_active ? '🟢 مفعل' : '🔴 غير مفعل';
            echo "  - {$type->name} ({$type->slug}): {$type->default_commission_rate}% {$status}\n";
        }
    }

    echo "\n📊 إحصائيات إضافية:\n";

    // التحقق من الجداول الأخرى
    $vendorSettings = DB::table('mp_vendor_commission_settings')->count();
    $commissionDetails = DB::table('mp_commission_details')->count();
    $consultations = DB::table('mp_consultations')->count();

    echo "  - إعدادات البائعين: {$vendorSettings}\n";
    echo "  - تفاصيل العمولات: {$commissionDetails}\n";
    echo "  - الاستشارات: {$consultations}\n";

    echo "\n✅ النظام جاهز للاستخدام!\n";
    echo "\n🔗 الروابط المتاحة:\n";
    echo "  - للأدمن: /admin/commission-management\n";
    echo "  - للبائعين: /vendor/commission\n";
    echo "  - الاستشارات: /vendor/consultations\n";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "📍 الملف: " . $e->getFile() . "\n";
    echo "📍 السطر: " . $e->getLine() . "\n";
    exit(1);
}

echo "\n🏁 انتهى التحقق!\n";