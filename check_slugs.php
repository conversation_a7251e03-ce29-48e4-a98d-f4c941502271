<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Checking course slugs:\n";
$courses = \Botble\TrainingAppointment\Models\Course::select('id', 'title', 'slug')->get();
foreach ($courses as $course) {
    echo "ID: {$course->id} - Title: {$course->title} - Slug: {$course->slug}\n";
}

echo "\nChecking category slugs:\n";
$categories = \Botble\TrainingAppointment\Models\CourseCategory::select('id', 'name', 'slug')->get();
foreach ($categories as $category) {
    echo "ID: {$category->id} - Name: {$category->name} - Slug: {$category->slug}\n";
}