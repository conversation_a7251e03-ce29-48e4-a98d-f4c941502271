<?php

echo "=== تنظيف الـ Cache للترجمات العربية ===\n\n";

// قائمة الأوامر المطلوبة
$commands = [
    'php artisan cache:clear' => 'تنظيف الـ cache العام',
    'php artisan config:clear' => 'تنظيف الـ config cache',
    'php artisan view:clear' => 'تنظيف الـ view cache',
    'php artisan route:clear' => 'تنظيف الـ route cache',
    'php artisan optimize:clear' => 'تنظيف جميع أنواع الـ cache',
];

foreach ($commands as $command => $description) {
    echo "🔄 {$description}...\n";
    echo "   تشغيل: {$command}\n";
    
    $output = [];
    $returnCode = 0;
    exec($command . ' 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "   ✅ تم بنجاح\n";
    } else {
        echo "   ❌ فشل: " . implode("\n", $output) . "\n";
    }
    echo "\n";
}

echo "=== تم الانتهاء من تنظيف الـ Cache ===\n\n";

echo "📝 خطوات إضافية يدوية:\n";
echo "1. تأكد من أن اللغة العربية مفعلة في لوحة التحكم\n";
echo "2. تأكد من أن جميع الإضافات مفعلة:\n";
echo "   - Marketplace\n";
echo "   - Training Appointment\n";
echo "   - Vendor Reels\n";
echo "3. إذا لم تظهر الترجمات، جرب:\n";
echo "   php artisan vendor:publish --tag=lang --force\n";
echo "4. تحديث الصفحة في المتصفح (Ctrl+F5)\n";
echo "5. تسجيل الخروج وإعادة تسجيل الدخول\n\n";

echo "🎯 النتيجة المتوقعة:\n";
echo "يجب أن تظهر القائمة الجانبية للفيندور بالترجمات العربية التالية:\n";
echo "- الدورات التدريبية\n";
echo "- دروس الدورات\n";
echo "- الخدمات\n";
echo "- المواعيد\n";
echo "- الريلز (إذا كان مفعل)\n";
echo "- العمولات والأرباح\n";
