<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

echo "=== فحص شامل لمشكلة روابط فئات الدورات ===\n\n";

// 1. فحص الروابط المسجلة
echo "1. فحص الروابط المسجلة:\n";
$routes = Route::getRoutes();
$courseRoutes = [];
foreach ($routes as $route) {
    $name = $route->getName();
    if ($name && (strpos($name, 'courses') !== false || strpos($name, 'training') !== false)) {
        $courseRoutes[] = [
            'name' => $name,
            'uri' => $route->uri(),
            'methods' => implode('|', $route->methods()),
            'action' => $route->getActionName()
        ];
    }
}

if (empty($courseRoutes)) {
    echo "   ❌ لم يتم العثور على أي روابط للدورات!\n";
} else {
    foreach ($courseRoutes as $route) {
        echo "   ✅ {$route['name']} -> {$route['uri']} [{$route['methods']}]\n";
    }
}

// 2. فحص قاعدة البيانات
echo "\n2. فحص قاعدة البيانات:\n";
try {
    $categoriesCount = DB::table('ta_course_categories')->count();
    $coursesCount = DB::table('ta_courses')->count();
    echo "   - عدد فئات الدورات: $categoriesCount\n";
    echo "   - عدد الدورات: $coursesCount\n";

    if ($categoriesCount > 0) {
        $categories = DB::table('ta_course_categories')
            ->select('id', 'name', 'slug', 'status')
            ->limit(5)
            ->get();
        echo "   - أمثلة على الفئات:\n";
        foreach ($categories as $cat) {
            echo "     * {$cat->name} (slug: {$cat->slug}, status: {$cat->status})\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
}

// 3. فحص ملفات العرض
echo "\n3. فحص ملفات العرض:\n";
$themeViewsPath = 'platform/themes/farmart/views/training-appointment';
$fullPath = __DIR__ . '/' . $themeViewsPath;

if (File::exists($fullPath)) {
    $files = File::files($fullPath);
    echo "   - مجلد العرض موجود: $themeViewsPath\n";
    echo "   - الملفات الموجودة:\n";
    foreach ($files as $file) {
        echo "     * " . $file->getFilename() . "\n";
    }
} else {
    echo "   ❌ مجلد العرض غير موجود: $themeViewsPath\n";

    // البحث عن مجلدات أخرى
    $themesPath = __DIR__ . '/platform/themes';
    if (File::exists($themesPath)) {
        $themes = File::directories($themesPath);
        echo "   - المواضيع المتاحة:\n";
        foreach ($themes as $theme) {
            $themeName = basename($theme);
            echo "     * $themeName\n";

            $trainingPath = $theme . '/views/training-appointment';
            if (File::exists($trainingPath)) {
                echo "       ✅ يحتوي على مجلد training-appointment\n";
                $trainingFiles = File::files($trainingPath);
                foreach ($trainingFiles as $file) {
                    echo "         - " . $file->getFilename() . "\n";
                }
            }
        }
    }
}

// 4. اختبار الروابط الفعلية
echo "\n4. اختبار الروابط الفعلية:\n";
$testUrls = [
    '/courses' => 'صفحة جميع الدورات',
    '/courses/category/care' => 'فئة الرعاية',
    '/courses/category/test-category' => 'فئة الاختبار'
];

foreach ($testUrls as $url => $description) {
    try {
        $request = Illuminate\Http\Request::create($url, 'GET');
        $response = app()->handle($request);
        $status = $response->getStatusCode();

        if ($status === 200) {
            echo "   ✅ $description ($url): يعمل بشكل صحيح\n";
        } else {
            echo "   ❌ $description ($url): خطأ $status\n";
            if ($status === 404) {
                echo "      - الرابط غير موجود\n";
            } elseif ($status === 500) {
                echo "      - خطأ في الخادم\n";
                $content = $response->getContent();
                if (strpos($content, 'View') !== false && strpos($content, 'not found') !== false) {
                    echo "      - ملف العرض مفقود\n";
                }
            }
        }
    } catch (Exception $e) {
        echo "   ❌ $description ($url): استثناء - " . $e->getMessage() . "\n";
    }
}

// 5. فحص الإعدادات
echo "\n5. فحص الإعدادات:\n";
try {
    // فحص الموضوع النشط
    $themeConfig = config('theme.theme');
    echo "   - الموضوع النشط: $themeConfig\n";

    // فحص حالة الإضافة
    echo "   - فحص حالة إضافة التدريب...\n";
} catch (Exception $e) {
    echo "   ❌ خطأ في فحص الإعدادات: " . $e->getMessage() . "\n";
}

// 6. فحص helper functions
echo "\n6. فحص helper functions:\n";
if (function_exists('get_course_category_url')) {
    echo "   ✅ get_course_category_url موجودة\n";
    try {
        $testUrl = get_course_category_url('care');
        echo "   - مثال URL: $testUrl\n";
    } catch (Exception $e) {
        echo "   ❌ خطأ في get_course_category_url: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ❌ get_course_category_url غير موجودة\n";
}

if (function_exists('get_course_url')) {
    echo "   ✅ get_course_url موجودة\n";
} else {
    echo "   ❌ get_course_url غير موجودة\n";
}

echo "\n=== انتهى الفحص الشامل ===\n";
