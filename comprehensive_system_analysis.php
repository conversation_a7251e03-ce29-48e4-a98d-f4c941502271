<?php

echo "=== تحليل شامل لنظام الدورات والخدمات والمواعيد ===\n\n";

// 1. فحص الملفات الأساسية
echo "1. فحص الملفات الأساسية:\n";

$files = [
    // Controllers
    'platform/plugins/training-appointment/src/Http/Controllers/Vendor/CourseController.php' => 'كنترولر الدورات للفيندور',
    'platform/plugins/training-appointment/src/Http/Controllers/Vendor/ServiceController.php' => 'كنترولر الخدمات للفيندور',
    'platform/plugins/training-appointment/src/Http/Controllers/Vendor/AppointmentController.php' => 'كنترولر المواعيد للفيندور',
    
    // Tables
    'platform/plugins/training-appointment/src/Tables/Vendor/CourseTable.php' => 'جدول الدورات للفيندور',
    'platform/plugins/training-appointment/src/Tables/Vendor/ServiceTable.php' => 'جدول الخدمات للفيندور',
    'platform/plugins/training-appointment/src/Tables/Vendor/AppointmentTable.php' => 'جدول المواعيد للفيندور',
    
    // Models
    'platform/plugins/training-appointment/src/Models/Course.php' => 'نموذج الدورة',
    'platform/plugins/training-appointment/src/Models/Service.php' => 'نموذج الخدمة',
    'platform/plugins/training-appointment/src/Models/Appointment.php' => 'نموذج الموعد',
    
    // Forms
    'platform/plugins/training-appointment/src/Forms/CourseForm.php' => 'نموذج الدورة',
    'platform/plugins/training-appointment/src/Forms/ServiceForm.php' => 'نموذج الخدمة',
    'platform/plugins/training-appointment/src/Forms/AppointmentForm.php' => 'نموذج الموعد',
    
    // Routes
    'platform/plugins/marketplace/routes/fronts.php' => 'مسارات الفيندور',
    'platform/plugins/training-appointment/routes/web.php' => 'مسارات الأدمن',
    
    // Service Providers
    'platform/plugins/training-appointment/src/Providers/TrainingAppointmentServiceProvider.php' => 'مزود الخدمة',
    'platform/plugins/marketplace/src/Providers/MarketplaceServiceProvider.php' => 'مزود خدمة المتجر',
    
    // Views
    'platform/plugins/training-appointment/resources/views/vendor/enrollments.blade.php' => 'صفحة المسجلين',
    'platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/menu.blade.php' => 'قائمة الفيندور',
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "  ✅ $description: موجود\n";
    } else {
        echo "  ❌ $description: مفقود\n";
    }
}

// 2. فحص المحتوى المهم
echo "\n2. فحص المحتوى المهم:\n";

// فحص الروتس في fronts.php
if (file_exists('platform/plugins/marketplace/routes/fronts.php')) {
    $content = file_get_contents('platform/plugins/marketplace/routes/fronts.php');
    
    $routes = [
        'vendor/courses' => 'مسار الدورات',
        'vendor/services' => 'مسار الخدمات', 
        'vendor/appointments' => 'مسار المواعيد',
        'CourseController' => 'كنترولر الدورات',
        'ServiceController' => 'كنترولر الخدمات',
        'AppointmentController' => 'كنترولر المواعيد',
    ];
    
    foreach ($routes as $search => $description) {
        if (strpos($content, $search) !== false) {
            echo "  ✅ $description: موجود في الروتس\n";
        } else {
            echo "  ❌ $description: مفقود من الروتس\n";
        }
    }
}

// فحص القائمة الجانبية في MarketplaceServiceProvider
if (file_exists('platform/plugins/marketplace/src/Providers/MarketplaceServiceProvider.php')) {
    $content = file_get_contents('platform/plugins/marketplace/src/Providers/MarketplaceServiceProvider.php');
    
    $menuItems = [
        'marketplace.vendor.courses' => 'عنصر الدورات في القائمة',
        'marketplace.vendor.services' => 'عنصر الخدمات في القائمة',
        'marketplace.vendor.appointments' => 'عنصر المواعيد في القائمة',
        "DashboardMenu::for('vendor')" => 'تسجيل في قائمة الفيندور',
    ];
    
    foreach ($menuItems as $search => $description) {
        if (strpos($content, $search) !== false) {
            echo "  ✅ $description: موجود\n";
        } else {
            echo "  ❌ $description: مفقود\n";
        }
    }
}

// 3. فحص النماذج للتأكد من وجود store_id
echo "\n3. فحص النماذج للتأكد من وجود store_id:\n";

$models = [
    'platform/plugins/training-appointment/src/Models/Course.php' => 'نموذج الدورة',
    'platform/plugins/training-appointment/src/Models/Service.php' => 'نموذج الخدمة',
    'platform/plugins/training-appointment/src/Models/Appointment.php' => 'نموذج الموعد',
];

foreach ($models as $file => $description) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (strpos($content, 'store_id') !== false) {
            echo "  ✅ $description: يحتوي على store_id\n";
        } else {
            echo "  ❌ $description: لا يحتوي على store_id\n";
        }
    } else {
        echo "  ❌ $description: الملف مفقود\n";
    }
}

// 4. فحص الكنترولرز للتأكد من الحماية
echo "\n4. فحص الكنترولرز للتأكد من الحماية:\n";

$controllers = [
    'platform/plugins/training-appointment/src/Http/Controllers/Vendor/CourseController.php' => 'كنترولر الدورات',
    'platform/plugins/training-appointment/src/Http/Controllers/Vendor/ServiceController.php' => 'كنترولر الخدمات',
    'platform/plugins/training-appointment/src/Http/Controllers/Vendor/AppointmentController.php' => 'كنترولر المواعيد',
];

foreach ($controllers as $file => $description) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $checks = [
            "auth('customer')->user()->store->id" => 'التحقق من المتجر',
            'store_id' => 'استخدام store_id',
            'abort(403)' => 'حماية الوصول',
        ];
        
        echo "  $description:\n";
        foreach ($checks as $search => $checkDesc) {
            if (strpos($content, $search) !== false) {
                echo "    ✅ $checkDesc: موجود\n";
            } else {
                echo "    ❌ $checkDesc: مفقود\n";
            }
        }
    } else {
        echo "  ❌ $description: الملف مفقود\n";
    }
}

echo "\n=== انتهى التحليل ===\n";
