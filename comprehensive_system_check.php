<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// بدء Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== فحص شامل لنظام ArtBella ===\n\n";

// 1. فحص الاتصال بقاعدة البيانات
echo "1. فحص الاتصال بقاعدة البيانات:\n";
try {
    DB::connection()->getPdo();
    echo "✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح\n";
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n";
}

// 2. فحص الإضافات المفعلة
echo "\n2. فحص الإضافات المفعلة:\n";
try {
    $activatedPlugins = DB::table('settings')->where('key', 'activated_plugins')->first();
    if ($activatedPlugins) {
        $plugins = json_decode($activatedPlugins->value, true);
        echo "الإضافات المفعلة (" . count($plugins) . "):\n";
        foreach ($plugins as $plugin) {
            echo "  ✅ $plugin\n";
        }
    } else {
        echo "❌ لا توجد إضافات مفعلة\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص الإضافات: " . $e->getMessage() . "\n";
}

// 3. فحص الجداول المطلوبة
echo "\n3. فحص الجداول المطلوبة:\n";
$requiredTables = [
    'users', 'settings', 'languages', 'ec_products', 'ec_product_categories',
    'ta_courses', 'ta_course_categories', 'ta_appointments', 'mp_stores',
    'ads', 'contacts', 'posts', 'pages'
];

foreach ($requiredTables as $table) {
    if (Schema::hasTable($table)) {
        $count = DB::table($table)->count();
        echo "  ✅ $table ($count سجل)\n";
    } else {
        echo "  ❌ $table (غير موجود)\n";
    }
}

// 4. فحص اللغات
echo "\n4. فحص اللغات:\n";
try {
    $languages = DB::table('languages')->get();
    foreach ($languages as $lang) {
        $default = $lang->lang_is_default ? ' (افتراضي)' : '';
        echo "  ✅ {$lang->lang_name} ({$lang->lang_locale})$default\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص اللغات: " . $e->getMessage() . "\n";
}

// 5. فحص إعدادات الدفع
echo "\n5. فحص إعدادات الدفع:\n";
try {
    $paymentSettings = DB::table('settings')
        ->where('key', 'like', 'payment_%_status')
        ->get();

    foreach ($paymentSettings as $setting) {
        $method = str_replace(['payment_', '_status'], '', $setting->key);
        $status = $setting->value == '1' ? 'مفعل' : 'معطل';
        echo "  " . ($setting->value == '1' ? '✅' : '❌') . " $method: $status\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص إعدادات الدفع: " . $e->getMessage() . "\n";
}

// 6. فحص الدورات التدريبية
echo "\n6. فحص الدورات التدريبية:\n";
try {
    if (Schema::hasTable('ta_courses')) {
        $coursesCount = DB::table('ta_courses')->count();
        $categoriesCount = DB::table('ta_course_categories')->count();
        echo "  ✅ عدد الدورات: $coursesCount\n";
        echo "  ✅ عدد فئات الدورات: $categoriesCount\n";

        // فحص الدورات المنشورة
        $publishedCourses = DB::table('ta_courses')->where('status', 'published')->count();
        echo "  ✅ الدورات المنشورة: $publishedCourses\n";
    } else {
        echo "  ❌ جدول الدورات غير موجود\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص الدورات: " . $e->getMessage() . "\n";
}

// 7. فحص المتاجر
echo "\n7. فحص المتاجر:\n";
try {
    if (Schema::hasTable('mp_stores')) {
        $storesCount = DB::table('mp_stores')->count();
        echo "  ✅ عدد المتاجر: $storesCount\n";

        // فحص حالة المتاجر
        if (Schema::hasColumn('mp_stores', 'is_approved')) {
            $activeStores = DB::table('mp_stores')->where('is_approved', 1)->count();
            echo "  ✅ المتاجر المعتمدة: $activeStores\n";
        } else {
            echo "  ⚠️ عمود is_approved غير موجود\n";
        }
    } else {
        echo "  ❌ جدول المتاجر غير موجود\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص المتاجر: " . $e->getMessage() . "\n";
}

// 8. فحص المنتجات
echo "\n8. فحص المنتجات:\n";
try {
    if (Schema::hasTable('ec_products')) {
        $productsCount = DB::table('ec_products')->count();
        $publishedProducts = DB::table('ec_products')->where('status', 'published')->count();
        echo "  ✅ عدد المنتجات: $productsCount\n";
        echo "  ✅ المنتجات المنشورة: $publishedProducts\n";
    } else {
        echo "  ❌ جدول المنتجات غير موجود\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص المنتجات: " . $e->getMessage() . "\n";
}

// 9. فحص الموضوع الحالي
echo "\n9. فحص الموضوع الحالي:\n";
try {
    $theme = DB::table('settings')->where('key', 'theme')->first();
    if ($theme) {
        echo "  ✅ الموضوع الحالي: {$theme->value}\n";

        // فحص وجود ملفات الموضوع
        $themePath = "platform/themes/{$theme->value}";
        if (is_dir($themePath)) {
            echo "  ✅ ملفات الموضوع موجودة\n";
        } else {
            echo "  ❌ ملفات الموضوع مفقودة\n";
        }
    } else {
        echo "  ❌ لم يتم تحديد موضوع\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص الموضوع: " . $e->getMessage() . "\n";
}

// 10. فحص الأذونات
echo "\n10. فحص أذونات الملفات:\n";
$directories = [
    'storage/app',
    'storage/framework',
    'storage/logs',
    'public/storage',
    'bootstrap/cache'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "  ✅ $dir (قابل للكتابة)\n";
        } else {
            echo "  ❌ $dir (غير قابل للكتابة)\n";
        }
    } else {
        echo "  ❌ $dir (غير موجود)\n";
    }
}

echo "\n=== انتهى الفحص ===\n";
