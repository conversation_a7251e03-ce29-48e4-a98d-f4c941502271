<?php

return [
    'name' => 'Ecommerce',
    'description' => 'Config email templates for Ecommerce',
    'templates' => array_filter([
        'welcome' => [
            'title' => 'Welcome',
            'description' => 'Send email to user when they registered an account on our site',
            'subject' => 'Welcome to {{ site_title }}!',
            'can_off' => true,
            'enabled' => false,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
            ],
        ],
        'confirm-email' => [
            'title' => 'Confirm email',
            'description' => 'Send email to user when they register an account to verify their email',
            'subject' => 'Confirm Email Notification',
            'can_off' => false,
            'variables' => [
                'verify_link' => 'Verify email link',
                'customer_name' => 'Customer name',
            ],
        ],
        'password-reminder' => [
            'title' => 'Reset password',
            'description' => 'Send email to user when requesting reset password',
            'subject' => 'Reset Password',
            'can_off' => false,
            'variables' => [
                'reset_link' => 'Reset password link',
                'customer_name' => 'Customer name',
            ],
        ],
        'customer_new_order' => [
            'title' => 'plugins/ecommerce::email.customer_new_order_title',
            'description' => 'plugins/ecommerce::email.customer_new_order_description',
            'subject' => 'New order(s) at {{ site_title }}',
            'can_off' => true,
            'enabled' => false,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
                'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
                'shipping_method' => 'plugins/ecommerce::ecommerce.shipping_method',
                'payment_method' => 'plugins/ecommerce::ecommerce.payment_method',
                'product_list' => 'plugins/ecommerce::ecommerce.product_list',
                'order_note' => 'plugins/ecommerce::ecommerce.order_note',
                'order_id' => 'plugins/ecommerce::ecommerce.order_id',
            ],
        ],
        'customer_cancel_order' => [
            'title' => 'plugins/ecommerce::email.order_cancellation_title',
            'description' => 'plugins/ecommerce::email.customer_order_cancellation_description',
            'subject' => 'Your order has been cancelled {{ order_id }}',
            'can_off' => true,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'order_id' => 'plugins/ecommerce::ecommerce.order_id',
                'cancellation_reason' => 'plugins/ecommerce::order.order_cancellation_reason',
                'product_list' => 'plugins/ecommerce::ecommerce.product_list',
            ],
        ],
        'admin_cancel_order' => [
            'title' => 'plugins/ecommerce::email.admin_order_cancellation_title',
            'description' => 'plugins/ecommerce::email.admin_order_cancellation_description',
            'subject' => 'Your order has been cancelled {{ order_id }}',
            'can_off' => true,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'order_id' => 'plugins/ecommerce::ecommerce.order_id',
                'cancellation_reason' => 'plugins/ecommerce::order.order_cancellation_reason',
                'product_list' => 'plugins/ecommerce::ecommerce.product_list',
            ],
        ],
        // Physical product emails - disabled for now
        /*
            'customer_delivery_order' => [
                'title' => 'plugins/ecommerce::email.delivery_confirmation_title',
                'description' => 'plugins/ecommerce::email.delivery_confirmation_description',
                'subject' => 'Order delivering {{ order_id }}',
                'can_off' => true,
                'variables' => [
                    'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                    'order_id' => 'plugins/ecommerce::ecommerce.order_id',
                    'order_delivery_notes' => 'Order delivery notes',
                    'product_list' => 'plugins/ecommerce::ecommerce.product_list',
                ],
            ],
            'customer_order_delivered' => [
                'title' => 'plugins/ecommerce::email.order_delivered_title',
                'description' => 'plugins/ecommerce::email.order_delivered_description',
                'subject' => 'Your order {{ order_id }} has been delivered',
                'can_off' => true,
                'enabled' => false,
                'variables' => [
                    'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                    'order_id' => 'plugins/ecommerce::ecommerce.order_id',
                    'order_delivery_notes' => 'Order delivery notes',
                    'product_list' => 'plugins/ecommerce::ecommerce.product_list',
                ],
            ],
        */ // : []),
        'admin_new_order' => [
            'title' => 'plugins/ecommerce::email.admin_new_order_title',
            'description' => 'plugins/ecommerce::email.admin_new_order_description',
            'subject' => 'New order(s) at {{ site_title }}',
            'can_off' => true,
            'enabled' => false,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
                'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
                'shipping_method' => 'plugins/ecommerce::ecommerce.shipping_method',
                'payment_method' => 'plugins/ecommerce::ecommerce.payment_method',
                'product_list' => 'plugins/ecommerce::ecommerce.product_list',
                'order_note' => 'plugins/ecommerce::ecommerce.order_note',
                'order_id' => 'plugins/ecommerce::ecommerce.order_id',
            ],
        ],
        'order_confirm' => [
            'title' => 'plugins/ecommerce::email.order_confirmation_title',
            'description' => 'plugins/ecommerce::email.order_confirmation_description',
            'subject' => 'Order confirmed {{ order_id }}',
            'can_off' => true,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
                'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
                'shipping_method' => 'plugins/ecommerce::ecommerce.shipping_method',
                'payment_method' => 'plugins/ecommerce::ecommerce.payment_method',
                'product_list' => 'plugins/ecommerce::ecommerce.product_list',
                'order_note' => 'plugins/ecommerce::ecommerce.order_note',
                'order_id' => 'plugins/ecommerce::ecommerce.order_id',
            ],
        ],
        'order_confirm_payment' => [
            'title' => 'plugins/ecommerce::email.payment_confirmation_title',
            'description' => 'plugins/ecommerce::email.payment_confirmation_description',
            'subject' => 'Payment for order {{ order_id }} was confirmed',
            'can_off' => true,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
                'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
                'shipping_method' => 'plugins/ecommerce::ecommerce.shipping_method',
                'payment_method' => 'plugins/ecommerce::ecommerce.payment_method',
                'product_list' => 'plugins/ecommerce::ecommerce.product_list',
                'order_note' => 'plugins/ecommerce::ecommerce.order_note',
                'order_id' => 'plugins/ecommerce::ecommerce.order_id',
            ],
        ],
        'order_recover' => [
            'title' => 'plugins/ecommerce::email.order_recover_title',
            'description' => 'plugins/ecommerce::email.order_recover_description',
            'subject' => 'Incomplete order',
            'can_off' => true,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
                'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
                'shipping_method' => 'plugins/ecommerce::ecommerce.shipping_method',
                'payment_method' => 'plugins/ecommerce::ecommerce.payment_method',
                'product_list' => 'plugins/ecommerce::ecommerce.product_list',
                'order_note' => 'plugins/ecommerce::ecommerce.order_note',
                'order_id' => 'plugins/ecommerce::ecommerce.order_id',
                'order_token' => 'plugins/ecommerce::ecommerce.order_token',
            ],
        ],
        'order-return-request' => [
            'title' => 'plugins/ecommerce::email.order_return_request_title',
            'description' => 'plugins/ecommerce::email.order_return_request_description',
            'subject' => 'Order return request',
            'can_off' => true,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
                'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
                'list_order_products' => 'List of order products',
                'order_id' => 'plugins/ecommerce::ecommerce.order_id',
                'order_note' => 'plugins/ecommerce::ecommerce.order_note',
                'return_reason' => 'plugins/ecommerce::order.order_return_reason',
                'product_list' => 'plugins/ecommerce::ecommerce.product_list',
            ],
        ],
        'invoice-payment-created' => [
            'title' => 'Invoice Payment Detail',
            'description' => 'Send a notification to the customer who makes order',
            'subject' => 'Payment received from {{ customer_name }} on {{ site_title }}',
            'can_off' => true,
            'enabled' => false,
            'variables' => [
                'customer_name' => 'Customer name',
                'invoice_code' => 'Invoice Code',
                'invoice_link' => 'Invoice Link',
            ],
        ],
        'review_products' => [
            'title' => 'Review Products',
            'description' => 'Send a notification to the customer to review the products when the order is completed',
            'subject' => 'Order completed, you can review the product',
            'can_off' => true,
            'enabled' => false,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'product_review_list' => 'plugins/ecommerce::ecommerce.product_review_list',
            ],
        ],
        'download_digital_products' => [
            'title' => 'Download digital products',
            'description' => 'Send email digital product downloads when guest makes a purchase',
            'subject' => 'Download digital products you have purchased',
            'can_off' => false,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
                'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
                'payment_method' => 'plugins/ecommerce::ecommerce.payment_method',
                'order_note' => 'plugins/ecommerce::ecommerce.order_note',
                'order_id' => 'plugins/ecommerce::ecommerce.order_id',
                'digital_product_list' => 'Digital product list',
                'digital_products' => 'Digital products',
            ],
        ],
        'customer-deletion-request-confirmation' => [
            'title' => 'Account deletion confirmation',
            'description' => 'Send confirmation email to user when they request to delete their account',
            'subject' => 'Confirm your account deletion request',
            'can_off' => false,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'customer_email' => 'plugins/ecommerce::ecommerce.customer_email',
                'confirm_url' => 'plugins/ecommerce::account-deletion.confirm_url',
            ],
        ],
        'customer-deletion-request-completed' => [
            'title' => 'Account deletion completed',
            'description' => 'Send email to user when their account is deleted',
            'subject' => 'Account deletion completed',
            'can_off' => false,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
            ],
        ],
        'order-return-status-updated' => [
            'title' => 'Order return request status updated',
            'description' => 'Notify the customer when their order return request status is updated',
            'subject' => 'Your order return request {{ order_id }} has been {{ status }}',
            'can_off' => true,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'order_id' => 'plugins/ecommerce::ecommerce.order_id',
                'description' => 'core/base::forms.description',
                'status' => 'core/base::forms.status',
                'product_list' => 'plugins/ecommerce::ecommerce.product_list',
            ],
        ],
        'payment-proof-upload-notification' => [
            'title' => 'Payment Proof Upload Notification',
            'description' => 'Notice to admin when customer uploads payment proof',
            'subject' => 'Payment proof uploaded by {{ customer_name }} for order {{ order_id }}',
            'can_off' => true,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'customer_email' => 'plugins/ecommerce::ecommerce.customer_email',
                'order_id' => 'plugins/ecommerce::ecommerce.order_id',
                'payment_link' => 'plugins/ecommerce::ecommerce.order_link',
                'order_link' => 'plugins/ecommerce::ecommerce.payment_link',
                'product_list' => 'plugins/ecommerce::ecommerce.product_list',
            ],
        ],
        'product-file-updated' => [
            'title' => 'Product File Updated',
            'description' => 'Notify customer when product files are updated',
            'subject' => 'Product Files Updated for {{ product_name }}',
            'can_off' => true,
            'enabled' => true,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'product_name' => 'plugins/ecommerce::products.product_name',
                'product_link' => 'plugins/ecommerce::products.product_link',
                'download_link' => 'plugins/ecommerce::ecommerce.download_link',
                'update_time' => 'plugins/ecommerce::ecommerce.update_time',
                'product_files' => 'plugins/ecommerce::ecommerce.product_files',
            ],
        ],
    ]),
];
