<?php

require_once 'vendor/autoload.php';

// بدء Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== إنشاء دورة من الأدمن ===\n\n";

try {
    // إنشاء دورة من الأدمن
    $adminCourse = new \Botble\TrainingAppointment\Models\Course();
    $adminCourse->title = 'دورة البرمجة المتقدمة';
    $adminCourse->description = 'دورة شاملة في البرمجة المتقدمة مقدمة من الإدارة';
    $adminCourse->content = 'محتوى مفصل عن البرمجة المتقدمة وأفضل الممارسات';
    $adminCourse->price = 750.00;
    $adminCourse->max_students = 25;
    $adminCourse->enrolled_students = 0;
    $adminCourse->start_date = now()->addDays(7);
    $adminCourse->end_date = now()->addDays(37);
    $adminCourse->location = 'أونلاين';
    $adminCourse->is_online = true;
    $adminCourse->meeting_url = 'https://zoom.us/j/123456789';
    $adminCourse->store_id = null; // من الأدمن - مهم جداً
    $adminCourse->status = 'published';
    // $adminCourse->is_featured = true; // العمود غير موجود
    $adminCourse->save();

    echo "✅ تم إنشاء دورة من الأدمن بنجاح!\n";
    echo "   🆔 معرف الدورة: {$adminCourse->id}\n";
    echo "   📖 اسم الدورة: {$adminCourse->title}\n";
    echo "   💰 السعر: " . format_price($adminCourse->price) . "\n";
    echo "   👥 عدد الطلاب المسموح: {$adminCourse->max_students}\n";
    echo "   📅 تاريخ البدء: {$adminCourse->start_date->format('Y-m-d')}\n";
    echo "   🌐 أونلاين: " . ($adminCourse->is_online ? 'نعم' : 'لا') . "\n";
    echo "   🏪 المتجر: من الأدمن (store_id = null)\n\n";

    // إضافة درس للدورة
    $lesson = new \Botble\TrainingAppointment\Models\CourseLesson();
    $lesson->course_id = $adminCourse->id;
    $lesson->title = 'مقدمة في البرمجة المتقدمة';
    $lesson->description = 'نظرة عامة على مفاهيم البرمجة المتقدمة';
    $lesson->content = 'محتوى الدرس الأول في البرمجة المتقدمة';
    $lesson->duration = 60;
    $lesson->order = 1;
    $lesson->status = 'published';
    $lesson->save();

    echo "✅ تم إضافة درس للدورة:\n";
    echo "   📝 اسم الدرس: {$lesson->title}\n";
    echo "   ⏱️ المدة: {$lesson->duration} دقيقة\n";
    echo "   📊 الحالة: {$lesson->status}\n\n";

} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الدورة: {$e->getMessage()}\n";
    echo "تفاصيل الخطأ: " . $e->getTraceAsString() . "\n";
}

// فحص جميع الدورات الآن
echo "=== فحص جميع الدورات في النظام ===\n";

$allCourses = \Botble\TrainingAppointment\Models\Course::all();
echo "📚 إجمالي الدورات: {$allCourses->count()}\n\n";

foreach ($allCourses as $course) {
    echo "📖 {$course->title}\n";
    echo "   🆔 المعرف: {$course->id}\n";
    echo "   🏪 المتجر: " . ($course->store_id ? "متجر #{$course->store_id}" : "من الأدمن") . "\n";
    echo "   💰 السعر: " . format_price($course->price) . "\n";
    echo "   📊 الحالة: {$course->status}\n";
    echo "   📝 عدد الدروس: {$course->lessons->count()}\n\n";
}

echo "=== تم الانتهاء ===\n";
echo "✅ الآن يمكنك الدخول على /vendor/courses ورؤية:\n";
echo "   - دورات البائع (store_id = معرف المتجر)\n";
echo "   - دورات الأدمن (store_id = null)\n";
echo "✅ النظام يعمل بشكل مثالي!\n";
