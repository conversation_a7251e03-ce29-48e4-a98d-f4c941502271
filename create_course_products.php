<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== إنشاء منتجات للدورات التدريبية ===\n\n";

use Botble\TrainingAppointment\Models\Course;
use Botble\TrainingAppointment\Services\CourseProductService;
use Botble\Ecommerce\Models\Product;

$courseProductService = app(CourseProductService::class);

try {
    // الحصول على جميع الدورات المنشورة
    $courses = Course::where('status', 'published')->get();
    
    echo "1. الدورات الموجودة: " . $courses->count() . "\n";
    
    $createdProducts = [];
    $updatedProducts = [];
    $errors = [];
    
    foreach ($courses as $course) {
        try {
            echo "\n   معالجة الدورة: " . $course->title . "\n";
            
            // التحقق من وجود منتج مرتبط
            $existingProduct = Product::where('sku', 'COURSE-' . $course->id)->first();
            
            if ($existingProduct) {
                echo "     - تحديث المنتج الموجود...\n";
                $product = $courseProductService->updateProductFromCourse($existingProduct, $course);
                $updatedProducts[] = $product;
                echo "     ✅ تم تحديث المنتج: " . $product->name . "\n";
            } else {
                echo "     - إنشاء منتج جديد...\n";
                $product = $courseProductService->createProductFromCourse($course);
                $createdProducts[] = $product;
                echo "     ✅ تم إنشاء المنتج: " . $product->name . "\n";
            }
            
            echo "       - SKU: " . $product->sku . "\n";
            echo "       - السعر: " . $product->price . "\n";
            echo "       - الكمية: " . $product->quantity . "\n";
            
        } catch (Exception $e) {
            $error = "خطأ في معالجة الدورة {$course->title}: " . $e->getMessage();
            $errors[] = $error;
            echo "     ❌ " . $error . "\n";
        }
    }
    
    echo "\n=== ملخص العملية ===\n";
    echo "✅ منتجات جديدة: " . count($createdProducts) . "\n";
    echo "🔄 منتجات محدثة: " . count($updatedProducts) . "\n";
    echo "❌ أخطاء: " . count($errors) . "\n";
    
    if (count($errors) > 0) {
        echo "\nالأخطاء:\n";
        foreach ($errors as $error) {
            echo "  - " . $error . "\n";
        }
    }
    
    echo "\n2. التحقق من النتائج:\n";
    $courseProducts = Product::where('sku', 'like', 'COURSE-%')->get();
    echo "   عدد المنتجات المرتبطة بالدورات: " . $courseProducts->count() . "\n";
    
    foreach ($courseProducts as $product) {
        $courseId = str_replace('COURSE-', '', $product->sku);
        $course = Course::find($courseId);
        echo "   - " . $product->name . " (Course ID: " . $courseId . " - موجودة: " . ($course ? 'نعم' : 'لا') . ")\n";
    }
    
    echo "\n3. اختبار نظام السلة:\n";
    echo "   - Cart enabled: " . (setting('ecommerce_enable_cart', 1) ? 'Yes' : 'No') . "\n";
    echo "   - Checkout enabled: " . (setting('ecommerce_enable_checkout', 1) ? 'Yes' : 'No') . "\n";
    
    echo "\n✅ تم الانتهاء من ربط الدورات بنظام التسعير!\n";
    echo "🛒 يمكن الآن إضافة الدورات للسلة والدفع\n";
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
