<?php

require_once 'bootstrap/app.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🗺️ إنشاء مواقع تجريبية للمتاجر\n\n";

use Botble\TrainingAppointment\Models\StoreLocation;
use Botble\Marketplace\Models\Store;

try {
    // الحصول على المتاجر الموجودة
    $stores = Store::all();
    
    if ($stores->isEmpty()) {
        echo "❌ لا توجد متاجر في النظام\n";
        exit;
    }
    
    echo "📊 عدد المتاجر الموجودة: {$stores->count()}\n\n";
    
    // مواقع تجريبية في الرياض
    $locations = [
        [
            'latitude' => 24.7136,
            'longitude' => 46.6753,
            'address' => 'شارع الملك فهد، حي العليا',
            'city' => 'الرياض',
            'state' => 'الرياض',
            'country' => 'السعودية',
            'postal_code' => '11564',
        ],
        [
            'latitude' => 24.6877,
            'longitude' => 46.7219,
            'address' => 'طريق الملك عبدالعزيز، حي الملز',
            'city' => 'الرياض',
            'state' => 'الرياض',
            'country' => 'السعودية',
            'postal_code' => '11461',
        ],
        [
            'latitude' => 24.7453,
            'longitude' => 46.6727,
            'address' => 'شارع التحلية، حي المحمدية',
            'city' => 'الرياض',
            'state' => 'الرياض',
            'country' => 'السعودية',
            'postal_code' => '12363',
        ],
        [
            'latitude' => 24.6408,
            'longitude' => 46.7728,
            'address' => 'طريق الدمام، حي الروضة',
            'city' => 'الرياض',
            'state' => 'الرياض',
            'country' => 'السعودية',
            'postal_code' => '13213',
        ],
        [
            'latitude' => 24.8047,
            'longitude' => 46.6544,
            'address' => 'شارع الأمير سلطان، حي النخيل',
            'city' => 'الرياض',
            'state' => 'الرياض',
            'country' => 'السعودية',
            'postal_code' => '14323',
        ],
    ];
    
    $createdCount = 0;
    
    foreach ($stores as $index => $store) {
        // التحقق من وجود موقع للمتجر
        $existingLocation = StoreLocation::where('store_id', $store->id)->first();
        
        if ($existingLocation) {
            echo "⚠️ المتجر '{$store->name}' لديه موقع بالفعل\n";
            continue;
        }
        
        // اختيار موقع من القائمة (مع التكرار إذا كانت المتاجر أكثر من المواقع)
        $locationData = $locations[$index % count($locations)];
        
        // إضافة تنويع بسيط للمواقع المتكررة
        if ($index >= count($locations)) {
            $locationData['latitude'] += (rand(-100, 100) / 10000); // تنويع بسيط
            $locationData['longitude'] += (rand(-100, 100) / 10000);
        }
        
        // إنشاء الموقع
        $location = StoreLocation::create([
            'store_id' => $store->id,
            'latitude' => $locationData['latitude'],
            'longitude' => $locationData['longitude'],
            'address' => $locationData['address'],
            'city' => $locationData['city'],
            'state' => $locationData['state'],
            'country' => $locationData['country'],
            'postal_code' => $locationData['postal_code'],
            'service_radius' => rand(15, 50), // نطاق خدمة عشوائي
            'is_primary' => true,
        ]);
        
        echo "✅ تم إنشاء موقع للمتجر '{$store->name}' في {$location->address}\n";
        $createdCount++;
    }
    
    echo "\n📈 النتائج:\n";
    echo "  ✅ تم إنشاء {$createdCount} موقع جديد\n";
    echo "  📍 إجمالي المواقع: " . StoreLocation::count() . "\n";
    
    // عرض إحصائيات المواقع
    echo "\n📊 إحصائيات المواقع:\n";
    $citiesCount = StoreLocation::distinct('city')->count();
    echo "  🏙️ عدد المدن: {$citiesCount}\n";
    
    $avgRadius = StoreLocation::avg('service_radius');
    echo "  📏 متوسط نطاق الخدمة: " . round($avgRadius, 1) . " كم\n";
    
    echo "\n🎉 تم إنشاء المواقع التجريبية بنجاح!\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "📍 في الملف: " . $e->getFile() . " السطر: " . $e->getLine() . "\n";
}
