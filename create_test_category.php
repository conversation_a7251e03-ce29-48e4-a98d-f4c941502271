<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

try {
    // Check if test category already exists
    $existingCategory = DB::table('ta_course_categories')
        ->where('slug', 'test-category')
        ->first();

    if ($existingCategory) {
        echo "Test category already exists with ID: " . $existingCategory->id . "\n";
    } else {
        // Insert test category
        $categoryId = DB::table('ta_course_categories')->insertGetId([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'description' => 'Test category for debugging course category links',
            'status' => 'published',
            'order' => 0,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        echo "Test category created successfully with ID: $categoryId\n";
    }

    // List all categories
    $categories = DB::table('ta_course_categories')->get();
    echo "\nAll course categories:\n";
    foreach ($categories as $category) {
        echo "- ID: {$category->id}, Name: {$category->name}, Slug: {$category->slug}, Status: {$category->status}\n";
    }

    // Check if there are any courses in this category
    $coursesCount = DB::table('ta_courses')
        ->where('category_id', $existingCategory->id ?? $categoryId)
        ->count();

    echo "\nCourses in test category: $coursesCount\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
