<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Botble\TrainingAppointment\Models\CourseCategory;
use Botble\TrainingAppointment\Models\Course;
use Illuminate\Support\Str;

echo "إنشاء بيانات تجريبية...\n";

// إنشاء تصنيفات تجريبية
$categories = [
    ['name' => 'تصميم الجرافيك', 'description' => 'دورات تعليم التصميم والجرافيك', 'is_featured' => true],
    ['name' => 'البرمجة والتطوير', 'description' => 'دورات تعليم البرمجة وتطوير المواقع', 'is_featured' => true],
    ['name' => 'التسويق الرقمي', 'description' => 'دورات تعليم التسويق الإلكتروني', 'is_featured' => true],
    ['name' => 'إدارة الأعمال', 'description' => 'دورات تطوير مهارات إدارة الأعمال', 'is_featured' => false],
];

foreach ($categories as $catData) {
    $existing = CourseCategory::where('name', $catData['name'])->first();
    if (!$existing) {
        $category = new CourseCategory();
        $category->name = $catData['name'];
        $category->slug = Str::slug($catData['name']);
        $category->description = $catData['description'];
        $category->status = 'published';
        $category->is_featured = $catData['is_featured'];
        $category->order = 0;
        $category->save();
        echo "تم إنشاء التصنيف: " . $category->name . " - Slug: " . $category->slug . "\n";
    } else {
        if (empty($existing->slug)) {
            $existing->slug = Str::slug($existing->name);
            $existing->save();
        }
        echo "موجود مسبقاً: " . $existing->name . " - Slug: " . $existing->slug . "\n";
    }
}

// إنشاء دورات تجريبية
$courses = [
    [
        'title' => 'تعلم Adobe Photoshop من الصفر',
        'description' => 'دورة شاملة لتعلم برنامج الفوتوشوب',
        'category_name' => 'تصميم الجرافيك',
        'price' => 199.99,
        'is_featured' => true
    ],
    [
        'title' => 'تطوير المواقع بـ PHP و Laravel',
        'description' => 'تعلم تطوير التطبيقات الويب المتقدمة',
        'category_name' => 'البرمجة والتطوير',
        'price' => 299.99,
        'is_featured' => true
    ],
    [
        'title' => 'التسويق عبر وسائل التواصل الاجتماعي',
        'description' => 'إستراتيجيات التسويق الرقمي الحديثة',
        'category_name' => 'التسويق الرقمي',
        'price' => 149.99,
        'is_featured' => true
    ]
];

foreach ($courses as $courseData) {
    $category = CourseCategory::where('name', $courseData['category_name'])->first();
    if ($category) {
        $existing = Course::where('title', $courseData['title'])->first();
        if (!$existing) {
            try {
                $course = new Course();
                $course->title = $courseData['title'];
                $course->slug = Str::slug($courseData['title']);
                $course->description = $courseData['description'];
                $course->category_id = $category->id;
                $course->price = $courseData['price'];
                $course->status = 'published';
                $course->max_students = 50;
                $course->enrolled_students = 0;
                $course->is_online = true;
                $course->save();
                echo "تم إنشاء الدورة: " . $course->title . " - Slug: " . $course->slug . "\n";
            } catch (Exception $e) {
                echo "خطأ في إنشاء الدورة: " . $e->getMessage() . "\n";
            }
        } else {
            if (empty($existing->slug)) {
                $existing->slug = Str::slug($existing->title);
                $existing->save();
            }
            echo "موجودة مسبقاً: " . $existing->title . " - Slug: " . $existing->slug . "\n";
        }
    }
}

echo "تم الانتهاء من إنشاء البيانات التجريبية.\n";