<?php

require_once 'vendor/autoload.php';

use Botble\Marketplace\Models\Store;
use Botble\TrainingAppointment\Models\Service;

// بدء Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== إنشاء خدمات تجريبية ===\n\n";

// البحث عن أول متجر
$store = Store::first();

if (!$store) {
    echo "❌ لا يوجد متاجر في النظام\n";
    exit;
}

echo "✅ تم العثور على المتجر: {$store->name}\n";

// إنشاء خدمات تجريبية
$services = [
    [
        'name' => 'استشارة تصميم داخلي',
        'description' => 'استشارة شخصية لتصميم المنزل والديكور الداخلي مع خبير متخصص',
        'price' => 150.00,
        'duration' => 60,
        'is_featured' => true,
    ],
    [
        'name' => 'جلسة تدريب فردي',
        'description' => 'جلسة تدريب شخصية مخصصة حسب احتياجاتك',
        'price' => 200.00,
        'duration' => 90,
        'is_featured' => false,
    ],
    [
        'name' => 'استشارة سريعة',
        'description' => 'استشارة سريعة لحل مشكلة محددة',
        'price' => 75.00,
        'duration' => 30,
        'is_featured' => false,
    ],
    [
        'name' => 'ورشة عمل جماعية',
        'description' => 'ورشة عمل تفاعلية مع مجموعة صغيرة',
        'price' => 300.00,
        'duration' => 120,
        'is_featured' => true,
    ],
];

foreach ($services as $serviceData) {
    // التحقق من عدم وجود الخدمة مسبقاً
    $existingService = Service::where('name', $serviceData['name'])
        ->where('store_id', $store->id)
        ->first();
    
    if ($existingService) {
        echo "⚠️ الخدمة '{$serviceData['name']}' موجودة بالفعل\n";
        continue;
    }
    
    $service = new Service();
    $service->name = $serviceData['name'];
    $service->description = $serviceData['description'];
    $service->price = $serviceData['price'];
    $service->duration = $serviceData['duration'];
    $service->store_id = $store->id;
    $service->status = 'published';
    $service->is_featured = $serviceData['is_featured'];
    $service->save();
    
    echo "✅ تم إنشاء الخدمة: {$service->name} - {$service->price} ريال\n";
}

echo "\n=== تم الانتهاء من إنشاء الخدمات ===\n";

// عرض إحصائيات
$totalServices = Service::where('store_id', $store->id)->count();
$featuredServices = Service::where('store_id', $store->id)->where('is_featured', true)->count();

echo "\nإحصائيات:\n";
echo "- إجمالي الخدمات: {$totalServices}\n";
echo "- الخدمات المميزة: {$featuredServices}\n";
echo "- رابط المتجر: " . url("stores/{$store->slug}") . "\n";
echo "- رابط حجز موعد: " . url("stores/{$store->id}/book-appointment") . "\n";
