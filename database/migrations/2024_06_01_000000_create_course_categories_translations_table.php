<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('ta_course_categories_translations')) {
            Schema::create('ta_course_categories_translations', function (Blueprint $table) {
                $table->string('lang_code', 20);
                $table->foreignId('ta_course_categories_id');
                $table->string('name')->nullable();
                $table->text('description')->nullable();

                $table->primary(['lang_code', 'ta_course_categories_id'], 'ta_course_categories_translations_primary');
            });
        }

        if (!Schema::hasTable('ta_courses_translations')) {
            Schema::create('ta_courses_translations', function (Blueprint $table) {
                $table->string('lang_code', 20);
                $table->foreignId('ta_courses_id');
                $table->string('title')->nullable();
                $table->text('description')->nullable();
                $table->longText('content')->nullable();
                $table->string('location')->nullable();

                $table->primary(['lang_code', 'ta_courses_id'], 'ta_courses_translations_primary');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('ta_course_categories_translations');
        Schema::dropIfExists('ta_courses_translations');
    }
};
