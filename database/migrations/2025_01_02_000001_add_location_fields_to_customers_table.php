<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ec_customers', function (Blueprint $table) {
            // إضافة حقول الموقع الجغرافي
            $table->text('address')->nullable()->after('phone');
            $table->string('city', 120)->nullable()->after('address');
            $table->string('state', 120)->nullable()->after('city');
            $table->string('country', 120)->nullable()->after('state');
            $table->string('postal_code', 20)->nullable()->after('country');
            $table->decimal('latitude', 10, 8)->nullable()->after('postal_code');
            $table->decimal('longitude', 11, 8)->nullable()->after('latitude');
            
            // إضافة فهارس للبحث الجغرافي
            $table->index(['latitude', 'longitude'], 'customers_location_index');
            $table->index(['city'], 'customers_city_index');
            $table->index(['state'], 'customers_state_index');
            $table->index(['country'], 'customers_country_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ec_customers', function (Blueprint $table) {
            // حذف الفهارس أولاً
            $table->dropIndex('customers_location_index');
            $table->dropIndex('customers_city_index');
            $table->dropIndex('customers_state_index');
            $table->dropIndex('customers_country_index');
            
            // حذف الأعمدة
            $table->dropColumn([
                'address',
                'city', 
                'state',
                'country',
                'postal_code',
                'latitude',
                'longitude'
            ]);
        });
    }
};
