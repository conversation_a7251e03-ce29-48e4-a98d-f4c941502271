<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إنشاء جدول فئات البائعين
        Schema::create('vendor_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_ar')->nullable(); // الاسم بالعربية
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->text('description_ar')->nullable(); // الوصف بالعربية
            $table->string('icon')->nullable(); // أيقونة الفئة
            $table->string('color', 7)->default('#007bff'); // لون الفئة
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->index(['is_active', 'sort_order']);
        });

        // إضافة عمود الفئة لجدول المتاجر
        Schema::table('mp_stores', function (Blueprint $table) {
            $table->unsignedBigInteger('vendor_category_id')->nullable()->after('customer_id');
            $table->foreign('vendor_category_id')->references('id')->on('vendor_categories')->onDelete('set null');
            $table->index(['vendor_category_id']);
        });

        // إدراج الفئات الافتراضية
        $this->insertDefaultCategories();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mp_stores', function (Blueprint $table) {
            $table->dropForeign(['vendor_category_id']);
            $table->dropIndex(['vendor_category_id']);
            $table->dropColumn('vendor_category_id');
        });

        Schema::dropIfExists('vendor_categories');
    }

    /**
     * إدراج الفئات الافتراضية
     */
    private function insertDefaultCategories(): void
    {
        $categories = [
            [
                'name' => 'Gym & Fitness',
                'name_ar' => 'صالات رياضية ولياقة بدنية',
                'slug' => 'gym-fitness',
                'description' => 'Gyms, fitness centers, and sports facilities',
                'description_ar' => 'صالات الجيم ومراكز اللياقة البدنية والمرافق الرياضية',
                'icon' => 'fas fa-dumbbell',
                'color' => '#28a745',
                'sort_order' => 1,
            ],
            [
                'name' => 'Medical Clinic',
                'name_ar' => 'عيادات طبية',
                'slug' => 'medical-clinic',
                'description' => 'Medical clinics, healthcare centers, and medical services',
                'description_ar' => 'العيادات الطبية ومراكز الرعاية الصحية والخدمات الطبية',
                'icon' => 'fas fa-stethoscope',
                'color' => '#dc3545',
                'sort_order' => 2,
            ],
            [
                'name' => 'Men\'s Salon',
                'name_ar' => 'صالونات رجالية',
                'slug' => 'mens-salon',
                'description' => 'Men\'s hair salons, barbershops, and grooming services',
                'description_ar' => 'صالونات الحلاقة الرجالية ومحلات الحلاقة وخدمات التجميل للرجال',
                'icon' => 'fas fa-cut',
                'color' => '#6f42c1',
                'sort_order' => 3,
            ],
            [
                'name' => 'Beauty Salon',
                'name_ar' => 'صالونات تجميل',
                'slug' => 'beauty-salon',
                'description' => 'Beauty salons, spas, and cosmetic services',
                'description_ar' => 'صالونات التجميل والمنتجعات الصحية وخدمات التجميل',
                'icon' => 'fas fa-spa',
                'color' => '#e83e8c',
                'sort_order' => 4,
            ],
            [
                'name' => 'Training Center',
                'name_ar' => 'مراكز تدريب',
                'slug' => 'training-center',
                'description' => 'Training centers, educational institutes, and skill development',
                'description_ar' => 'مراكز التدريب والمعاهد التعليمية وتطوير المهارات',
                'icon' => 'fas fa-graduation-cap',
                'color' => '#fd7e14',
                'sort_order' => 5,
            ],
            [
                'name' => 'Other Services',
                'name_ar' => 'خدمات أخرى',
                'slug' => 'other-services',
                'description' => 'Other professional services and businesses',
                'description_ar' => 'خدمات مهنية أخرى وأعمال متنوعة',
                'icon' => 'fas fa-briefcase',
                'color' => '#6c757d',
                'sort_order' => 6,
            ],
        ];

        foreach ($categories as $category) {
            \DB::table('vendor_categories')->insert(array_merge($category, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
};
