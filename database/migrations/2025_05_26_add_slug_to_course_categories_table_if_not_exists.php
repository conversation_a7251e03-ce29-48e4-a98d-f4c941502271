<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('ta_course_categories', 'slug')) {
            Schema::table('ta_course_categories', function (Blueprint $table) {
                $table->string('slug')->nullable()->unique()->after('name');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('ta_course_categories', 'slug')) {
            Schema::table('ta_course_categories', function (Blueprint $table) {
                $table->dropColumn('slug');
            });
        }
    }
};