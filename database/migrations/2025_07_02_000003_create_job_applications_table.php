<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_applications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained('ec_customers')->onDelete('cascade');
            $table->string('full_name');
            $table->string('email');
            $table->string('phone');
            $table->integer('age')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->string('education_level')->nullable(); // ثانوي، جامعي، ماجستير، دكتوراه
            $table->integer('experience_years')->default(0);
            $table->string('cv_file')->nullable(); // مسار ملف السيرة الذاتية
            $table->text('cover_letter')->nullable(); // خطاب التقديم
            $table->foreignId('preferred_job_category_id')->nullable()->constrained('job_categories')->onDelete('set null');
            $table->decimal('preferred_salary_min', 10, 2)->nullable();
            $table->decimal('preferred_salary_max', 10, 2)->nullable();
            $table->enum('preferred_work_type', ['full_time', 'part_time', 'freelance', 'contract'])->default('full_time');
            $table->boolean('available_immediately')->default(true);
            $table->date('availability_date')->nullable();
            $table->string('city')->nullable();
            $table->boolean('can_relocate')->default(false);
            $table->enum('status', ['pending', 'under_review', 'approved', 'rejected', 'recommended'])->default('pending');
            $table->text('admin_notes')->nullable();
            $table->timestamps();
            
            $table->index(['status', 'created_at']);
            $table->index(['preferred_job_category_id', 'status']);
            $table->index(['city', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_applications');
    }
};
