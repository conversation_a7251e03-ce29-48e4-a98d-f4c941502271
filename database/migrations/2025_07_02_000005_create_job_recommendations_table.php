<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_recommendations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_application_id')->constrained('job_applications')->onDelete('cascade');
            $table->foreignId('store_id')->constrained('mp_stores')->onDelete('cascade');
            $table->foreignId('admin_id')->constrained('users')->onDelete('cascade');
            $table->enum('status', ['pending', 'viewed', 'interested', 'not_interested', 'hired'])->default('pending');
            $table->text('admin_notes')->nullable(); // ملاحظات الأدمن عند الترشيح
            $table->text('vendor_response')->nullable(); // رد الفيندور
            $table->timestamp('recommended_at')->nullable();
            $table->timestamp('responded_at')->nullable();
            $table->timestamps();
            
            $table->index(['store_id', 'status']);
            $table->index(['job_application_id', 'status']);
            $table->index(['status', 'recommended_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_recommendations');
    }
};
