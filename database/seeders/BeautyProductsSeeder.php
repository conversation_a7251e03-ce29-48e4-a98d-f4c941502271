<?php

namespace Database\Seeders;

use Botble\Base\Supports\BaseSeeder;
use Botble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\ProductCategory;
use Botble\Ecommerce\Models\Brand;
use Botble\Marketplace\Models\Store;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class BeautyProductsSeeder extends BaseSeeder
{
    public function run(): void
    {
        // إنشاء فئات منتجات الجمال أولاً
        $this->createBeautyCategories();
        
        // إنشاء ماركات الجمال
        $this->createBeautyBrands();
        
        // إضافة منتجات الجمال للمتاجر
        $this->createBeautyProducts();
    }

    private function createBeautyCategories(): void
    {
        $categories = [
            [
                'name' => 'العناية بالبشرة',
                'description' => 'منتجات العناية بالبشرة والوجه',
                'is_featured' => true,
                'icon' => 'fas fa-spa',
            ],
            [
                'name' => 'العناية بالشعر',
                'description' => 'منتجات العناية بالشعر وفروة الرأس',
                'is_featured' => true,
                'icon' => 'fas fa-cut',
            ],
            [
                'name' => 'المكياج',
                'description' => 'مستحضرات التجميل والمكياج',
                'is_featured' => true,
                'icon' => 'fas fa-palette',
            ],
            [
                'name' => 'العطور',
                'description' => 'العطور ومزيلات العرق',
                'is_featured' => true,
                'icon' => 'fas fa-spray-can',
            ],
            [
                'name' => 'العناية بالجسم',
                'description' => 'منتجات العناية بالجسم والاستحمام',
                'is_featured' => false,
                'icon' => 'fas fa-bath',
            ],
            [
                'name' => 'أدوات التجميل',
                'description' => 'فرش المكياج والأدوات',
                'is_featured' => false,
                'icon' => 'fas fa-tools',
            ],
        ];

        foreach ($categories as $index => $categoryData) {
            ProductCategory::firstOrCreate(
                ['name' => $categoryData['name']],
                [
                    'description' => $categoryData['description'],
                    'parent_id' => 0,
                    'order' => $index + 1,
                    'status' => 'published',
                    'is_featured' => $categoryData['is_featured'],
                    'icon' => $categoryData['icon'],
                ]
            );
        }

        $this->command->info("تم إنشاء فئات منتجات الجمال");
    }

    private function createBeautyBrands(): void
    {
        $brands = [
            ['name' => 'L\'Oréal', 'description' => 'ماركة عالمية للعناية والجمال'],
            ['name' => 'Nivea', 'description' => 'منتجات العناية بالبشرة'],
            ['name' => 'Maybelline', 'description' => 'مستحضرات التجميل'],
            ['name' => 'Garnier', 'description' => 'العناية الطبيعية بالشعر والبشرة'],
            ['name' => 'MAC', 'description' => 'مكياج احترافي'],
            ['name' => 'The Body Shop', 'description' => 'منتجات طبيعية للعناية'],
            ['name' => 'Pantene', 'description' => 'العناية بالشعر'],
            ['name' => 'Dove', 'description' => 'منتجات العناية اليومية'],
        ];

        foreach ($brands as $brandData) {
            Brand::firstOrCreate(
                ['name' => $brandData['name']],
                [
                    'description' => $brandData['description'],
                    'status' => 'published',
                    'is_featured' => true,
                ]
            );
        }

        $this->command->info("تم إنشاء ماركات الجمال");
    }

    private function createBeautyProducts(): void
    {
        $stores = Store::all();
        $categories = ProductCategory::pluck('id', 'name');
        $brands = Brand::pluck('id', 'name');

        if ($stores->isEmpty()) {
            $this->command->error("لا توجد متاجر في قاعدة البيانات");
            return;
        }

        $this->command->info("بدء إضافة منتجات الجمال لـ " . $stores->count() . " متجر");

        foreach ($stores as $store) {
            $this->createProductsForStore($store, $categories, $brands);
        }

        $this->command->info("تم الانتهاء من إضافة منتجات الجمال لجميع المتاجر");
    }

    private function createProductsForStore(Store $store, $categories, $brands): void
    {
        // تحديد نوع المتجر بناءً على الاسم
        $storeType = $this->determineStoreType($store);
        
        // الحصول على المنتجات المناسبة لنوع المتجر
        $products = $this->getProductsForStoreType($storeType);
        
        $createdCount = 0;
        
        foreach ($products as $productData) {
            try {
                // التحقق من عدم وجود المنتج مسبقاً
                $existingProduct = Product::where('name', $productData['name'])
                    ->where('store_id', $store->id)
                    ->first();
                
                if ($existingProduct) {
                    continue;
                }

                $categoryId = $categories->get($productData['category']);
                $brandId = $brands->get($productData['brand'] ?? 'L\'Oréal');

                Product::create([
                    'name' => $productData['name'],
                    'description' => $productData['description'],
                    'content' => $productData['content'],
                    'price' => $productData['price'],
                    'sale_price' => $productData['sale_price'] ?? null,
                    'sku' => $this->generateSKU($productData['name'], $store->id),
                    'status' => 'published',
                    'is_featured' => $productData['is_featured'] ?? false,
                    'brand_id' => $brandId,
                    'store_id' => $store->id,
                    'quantity' => rand(10, 100),
                    'allow_checkout_when_out_of_stock' => false,
                    'with_storehouse_management' => true,
                    'stock_status' => 'in_stock',
                    'weight' => $productData['weight'] ?? rand(50, 500),
                    'length' => rand(5, 15),
                    'wide' => rand(5, 15),
                    'height' => rand(5, 15),
                    'image' => null, // سيتم إضافة الصور لاحقاً
                    'images' => json_encode([]),
                ]);

                // ربط المنتج بالفئة
                if ($categoryId) {
                    DB::table('ec_product_category_product')->insert([
                        'product_id' => DB::getPdo()->lastInsertId(),
                        'category_id' => $categoryId,
                    ]);
                }
                
                $createdCount++;
                
            } catch (\Exception $e) {
                $this->command->error("خطأ في إنشاء المنتج {$productData['name']} للمتجر {$store->name}: " . $e->getMessage());
            }
        }
        
        $this->command->info("تم إنشاء {$createdCount} منتج للمتجر: {$store->name}");
    }

    private function determineStoreType(Store $store): string
    {
        $name = strtolower($store->name);
        
        if (str_contains($name, 'سبا') || str_contains($name, 'spa')) {
            return 'spa';
        } elseif (str_contains($name, 'صالون') || str_contains($name, 'salon')) {
            return 'salon';
        } elseif (str_contains($name, 'متجر') || str_contains($name, 'store') || str_contains($name, 'shop')) {
            return 'store';
        } elseif (str_contains($name, 'مركز') || str_contains($name, 'center') || str_contains($name, 'عيادة') || str_contains($name, 'clinic')) {
            return 'clinic';
        } else {
            return 'general';
        }
    }

    private function generateSKU(string $productName, int $storeId): string
    {
        $prefix = strtoupper(substr(Str::slug($productName), 0, 3));
        $suffix = str_pad($storeId, 3, '0', STR_PAD_LEFT);
        $random = rand(100, 999);
        
        return $prefix . '-' . $suffix . '-' . $random;
    }

    private function getProductsForStoreType(string $storeType): array
    {
        switch ($storeType) {
            case 'spa':
                return $this->getSpaProducts();
            case 'salon':
                return $this->getSalonProducts();
            case 'store':
                return $this->getStoreProducts();
            case 'clinic':
                return $this->getClinicProducts();
            default:
                return $this->getGeneralProducts();
        }
    }

    private function getSpaProducts(): array
    {
        return [
            [
                'name' => 'زيت التدليك بالأعشاب الطبيعية',
                'description' => 'زيت تدليك طبيعي بخلاصة الأعشاب للاسترخاء',
                'content' => 'زيت تدليك فاخر مصنوع من خلاصة الأعشاب الطبيعية، يوفر تجربة استرخاء مثالية ويرطب البشرة.',
                'category' => 'العناية بالجسم',
                'brand' => 'The Body Shop',
                'price' => 150,
                'sale_price' => 120,
                'is_featured' => true,
                'weight' => 250,
            ],
            [
                'name' => 'ماسك الطين الطبيعي للوجه',
                'description' => 'ماسك طين طبيعي لتنظيف البشرة بعمق',
                'content' => 'ماسك طين غني بالمعادن الطبيعية، ينظف المسام بعمق ويترك البشرة نظيفة ومشرقة.',
                'category' => 'العناية بالبشرة',
                'brand' => 'The Body Shop',
                'price' => 80,
                'sale_price' => 65,
                'is_featured' => false,
                'weight' => 100,
            ],
            [
                'name' => 'أملاح الاستحمام بالخزامى',
                'description' => 'أملاح استحمام طبيعية برائحة الخزامى المهدئة',
                'content' => 'أملاح استحمام فاخرة بخلاصة الخزامى الطبيعية، تساعد على الاسترخاء وتنعيم البشرة.',
                'category' => 'العناية بالجسم',
                'brand' => 'The Body Shop',
                'price' => 60,
                'is_featured' => false,
                'weight' => 300,
            ],
        ];
    }

    private function getSalonProducts(): array
    {
        return [
            [
                'name' => 'شامبو للشعر الجاف والمتضرر',
                'description' => 'شامبو مرطب للشعر الجاف والمتضرر',
                'content' => 'شامبو غني بالزيوت الطبيعية والفيتامينات، يرطب الشعر الجاف ويصلح التلف.',
                'category' => 'العناية بالشعر',
                'brand' => 'Pantene',
                'price' => 45,
                'sale_price' => 35,
                'is_featured' => true,
                'weight' => 400,
            ],
            [
                'name' => 'بلسم مرطب للشعر',
                'description' => 'بلسم مرطب يترك الشعر ناعماً ولامعاً',
                'content' => 'بلسم مرطب بتركيبة متقدمة، يغذي الشعر ويتركه ناعماً وسهل التصفيف.',
                'category' => 'العناية بالشعر',
                'brand' => 'Pantene',
                'price' => 40,
                'is_featured' => false,
                'weight' => 300,
            ],
            [
                'name' => 'كريم أساس طويل المفعول',
                'description' => 'كريم أساس يدوم طوال اليوم',
                'content' => 'كريم أساس بتركيبة مقاومة للماء والعرق، يوفر تغطية مثالية تدوم حتى 12 ساعة.',
                'category' => 'المكياج',
                'brand' => 'Maybelline',
                'price' => 85,
                'sale_price' => 70,
                'is_featured' => true,
                'weight' => 30,
            ],
            [
                'name' => 'أحمر شفاه مات',
                'description' => 'أحمر شفاه بلمسة نهائية مطفية',
                'content' => 'أحمر شفاه بتركيبة مطفية مريحة، يوفر لوناً غنياً يدوم طويلاً دون جفاف.',
                'category' => 'المكياج',
                'brand' => 'MAC',
                'price' => 120,
                'is_featured' => true,
                'weight' => 15,
            ],
        ];
    }

    private function getStoreProducts(): array
    {
        return [
            [
                'name' => 'مجموعة فرش المكياج الاحترافية',
                'description' => 'مجموعة فرش مكياج احترافية للوجه والعيون',
                'content' => 'مجموعة من 12 فرشاة مكياج احترافية بشعيرات طبيعية وصناعية عالية الجودة.',
                'category' => 'أدوات التجميل',
                'brand' => 'MAC',
                'price' => 200,
                'sale_price' => 160,
                'is_featured' => true,
                'weight' => 150,
            ],
            [
                'name' => 'باليت ظلال العيون',
                'description' => 'باليت ظلال عيون بـ 12 لون',
                'content' => 'باليت ظلال عيون بـ 12 لون متنوع، من الألوان الطبيعية إلى الجريئة.',
                'category' => 'المكياج',
                'brand' => 'Maybelline',
                'price' => 95,
                'sale_price' => 75,
                'is_featured' => true,
                'weight' => 80,
            ],
        ];
    }

    private function getClinicProducts(): array
    {
        return [
            [
                'name' => 'سيروم فيتامين سي للوجه',
                'description' => 'سيروم مضاد للأكسدة بفيتامين سي',
                'content' => 'سيروم مركز بفيتامين سي النقي، يحارب علامات التقدم في السن ويوحد لون البشرة.',
                'category' => 'العناية بالبشرة',
                'brand' => 'L\'Oréal',
                'price' => 180,
                'sale_price' => 150,
                'is_featured' => true,
                'weight' => 30,
            ],
            [
                'name' => 'كريم مضاد للتجاعيد',
                'description' => 'كريم ليلي مضاد للتجاعيد',
                'content' => 'كريم ليلي بتركيبة متقدمة من الريتينول والببتيدات، يقلل من ظهور التجاعيد.',
                'category' => 'العناية بالبشرة',
                'brand' => 'L\'Oréal',
                'price' => 220,
                'is_featured' => true,
                'weight' => 50,
            ],
        ];
    }

    private function getGeneralProducts(): array
    {
        return [
            [
                'name' => 'كريم مرطب للوجه',
                'description' => 'كريم مرطب يومي للوجه',
                'content' => 'كريم مرطب خفيف مناسب لجميع أنواع البشرة، يوفر ترطيباً يدوم 24 ساعة.',
                'category' => 'العناية بالبشرة',
                'brand' => 'Nivea',
                'price' => 55,
                'sale_price' => 45,
                'is_featured' => false,
                'weight' => 75,
            ],
            [
                'name' => 'غسول الوجه المنظف',
                'description' => 'غسول وجه لطيف للاستخدام اليومي',
                'content' => 'غسول وجه لطيف ينظف البشرة بعمق دون جفاف، مناسب للاستخدام اليومي.',
                'category' => 'العناية بالبشرة',
                'brand' => 'Dove',
                'price' => 35,
                'is_featured' => false,
                'weight' => 150,
            ],
        ];
    }
}
