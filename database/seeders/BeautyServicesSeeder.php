<?php

namespace Database\Seeders;

use Botble\Base\Supports\BaseSeeder;
use Botble\Marketplace\Models\Store;
use Botble\TrainingAppointment\Models\Service;
use Illuminate\Support\Facades\DB;

class BeautyServicesSeeder extends BaseSeeder
{
    public function run(): void
    {
        // مسح الخدمات الحالية للمتاجر الجديدة فقط
        $this->cleanupExistingServices();
        
        // إضافة خدمات الجمال لجميع المتاجر الجديدة
        $this->createBeautyServices();
    }

    private function cleanupExistingServices(): void
    {
        // مسح الخدمات للمتاجر الجديدة فقط (التي تم إنشاؤها اليوم)
        $newStores = Store::whereDate('created_at', today())->pluck('id');
        
        if ($newStores->isNotEmpty()) {
            Service::whereIn('store_id', $newStores)->delete();
            $this->command->info("تم مسح الخدمات الحالية للمتاجر الجديدة");
        }
    }

    private function createBeautyServices(): void
    {
        // الحصول على جميع المتاجر
        $stores = Store::all();
        
        if ($stores->isEmpty()) {
            $this->command->error("لا توجد متاجر في قاعدة البيانات");
            return;
        }

        $this->command->info("بدء إضافة خدمات الجمال لـ " . $stores->count() . " متجر");

        foreach ($stores as $store) {
            $this->createServicesForStore($store);
        }

        $this->command->info("تم الانتهاء من إضافة خدمات الجمال لجميع المتاجر");
    }

    private function createServicesForStore(Store $store): void
    {
        // تحديد نوع المتجر بناءً على الاسم والوصف
        $storeType = $this->determineStoreType($store);
        
        // الحصول على الخدمات المناسبة لنوع المتجر
        $services = $this->getServicesForStoreType($storeType);
        
        $createdCount = 0;
        
        foreach ($services as $serviceData) {
            try {
                Service::create([
                    'name' => $serviceData['name'],
                    'description' => $serviceData['description'],
                    'category' => $serviceData['category'],
                    'price' => $serviceData['price'],
                    'duration' => $serviceData['duration'],
                    'status' => 'published',
                    'is_featured' => $serviceData['is_featured'],
                    'store_id' => $store->id,
                ]);
                
                $createdCount++;
                
            } catch (\Exception $e) {
                $this->command->error("خطأ في إنشاء الخدمة {$serviceData['name']} للمتجر {$store->name}: " . $e->getMessage());
            }
        }
        
        $this->command->info("تم إنشاء {$createdCount} خدمة للمتجر: {$store->name}");
    }

    private function determineStoreType(Store $store): string
    {
        $name = strtolower($store->name);
        $description = strtolower($store->description ?? '');
        
        if (str_contains($name, 'سبا') || str_contains($name, 'spa')) {
            return 'spa';
        } elseif (str_contains($name, 'صالون') || str_contains($name, 'salon')) {
            return 'salon';
        } elseif (str_contains($name, 'متجر') || str_contains($name, 'store') || str_contains($name, 'shop')) {
            return 'store';
        } elseif (str_contains($name, 'مركز') || str_contains($name, 'center') || str_contains($name, 'عيادة') || str_contains($name, 'clinic')) {
            return 'clinic';
        } else {
            // افتراضي: صالون
            return 'salon';
        }
    }

    private function getServicesForStoreType(string $storeType): array
    {
        switch ($storeType) {
            case 'spa':
                return $this->getSpaServices();
            case 'salon':
                return $this->getSalonServices();
            case 'store':
                return $this->getStoreServices();
            case 'clinic':
                return $this->getClinicServices();
            default:
                return $this->getSalonServices();
        }
    }

    private function getSpaServices(): array
    {
        return [
            [
                'name' => 'جلسة تدليك استرخائي',
                'description' => 'جلسة تدليك كامل للجسم للاسترخاء وتخفيف التوتر',
                'category' => 'تدليك وسبا',
                'price' => 250,
                'duration' => 90,
                'is_featured' => true,
            ],
            [
                'name' => 'تدليك بالأحجار الساخنة',
                'description' => 'تدليك علاجي باستخدام الأحجار الساخنة لتخفيف آلام العضلات',
                'category' => 'تدليك وسبا',
                'price' => 300,
                'duration' => 120,
                'is_featured' => true,
            ],
            [
                'name' => 'حمام بخار عشبي',
                'description' => 'جلسة بخار بالأعشاب الطبيعية لتنظيف البشرة',
                'category' => 'تدليك وسبا',
                'price' => 150,
                'duration' => 45,
                'is_featured' => false,
            ],
            [
                'name' => 'علاج بالطين الطبيعي',
                'description' => 'قناع طين طبيعي للوجه والجسم لتنقية البشرة',
                'category' => 'عناية بالبشرة',
                'price' => 200,
                'duration' => 60,
                'is_featured' => true,
            ],
            [
                'name' => 'جلسة هيدروثيرابي',
                'description' => 'علاج مائي للاسترخاء وتحسين الدورة الدموية',
                'category' => 'تدليك وسبا',
                'price' => 350,
                'duration' => 75,
                'is_featured' => true,
            ],
            [
                'name' => 'تقشير الجسم بالملح البحري',
                'description' => 'تقشير طبيعي للجسم باستخدام الأملاح البحرية',
                'category' => 'عناية بالبشرة',
                'price' => 180,
                'duration' => 50,
                'is_featured' => false,
            ],
        ];
    }

    private function getSalonServices(): array
    {
        return [
            [
                'name' => 'قص وتصفيف الشعر',
                'description' => 'قص شعر احترافي مع تصفيف حسب الطلب',
                'category' => 'العناية بالشعر',
                'price' => 120,
                'duration' => 60,
                'is_featured' => true,
            ],
            [
                'name' => 'صبغة الشعر الكاملة',
                'description' => 'صبغة شعر احترافية بألوان متنوعة',
                'category' => 'العناية بالشعر',
                'price' => 200,
                'duration' => 120,
                'is_featured' => true,
            ],
            [
                'name' => 'فرد الشعر بالكيراتين',
                'description' => 'علاج فرد الشعر بالكيراتين الطبيعي',
                'category' => 'العناية بالشعر',
                'price' => 400,
                'duration' => 180,
                'is_featured' => true,
            ],
            [
                'name' => 'مكياج يومي',
                'description' => 'مكياج خفيف وطبيعي للاستخدام اليومي',
                'category' => 'مكياج',
                'price' => 100,
                'duration' => 45,
                'is_featured' => false,
            ],
            [
                'name' => 'مكياج سهرة',
                'description' => 'مكياج احترافي للمناسبات والسهرات',
                'category' => 'مكياج',
                'price' => 180,
                'duration' => 75,
                'is_featured' => true,
            ],
            [
                'name' => 'مانيكير وبديكير',
                'description' => 'العناية الكاملة بالأظافر لليدين والقدمين',
                'category' => 'العناية بالأظافر',
                'price' => 80,
                'duration' => 90,
                'is_featured' => false,
            ],
            [
                'name' => 'تنظيف البشرة',
                'description' => 'تنظيف عميق للبشرة وإزالة الرؤوس السوداء',
                'category' => 'عناية بالبشرة',
                'price' => 150,
                'duration' => 60,
                'is_featured' => true,
            ],
            [
                'name' => 'إزالة الشعر بالشمع',
                'description' => 'إزالة الشعر الزائد باستخدام الشمع الطبيعي',
                'category' => 'إزالة الشعر',
                'price' => 100,
                'duration' => 45,
                'is_featured' => false,
            ],
        ];
    }

    private function getStoreServices(): array
    {
        return [
            [
                'name' => 'استشارة تجميل شخصية',
                'description' => 'استشارة مع خبير تجميل لاختيار المنتجات المناسبة',
                'category' => 'استشارات',
                'price' => 50,
                'duration' => 30,
                'is_featured' => true,
            ],
            [
                'name' => 'تطبيق مكياج تجريبي',
                'description' => 'تجربة المكياج قبل الشراء مع خبير متخصص',
                'category' => 'مكياج',
                'price' => 75,
                'duration' => 45,
                'is_featured' => true,
            ],
            [
                'name' => 'تحليل نوع البشرة',
                'description' => 'تحليل احترافي لنوع البشرة واحتياجاتها',
                'category' => 'عناية بالبشرة',
                'price' => 80,
                'duration' => 30,
                'is_featured' => false,
            ],
            [
                'name' => 'ورشة تعليم المكياج',
                'description' => 'ورشة تعليمية لتعلم أساسيات المكياج',
                'category' => 'تعليم',
                'price' => 200,
                'duration' => 120,
                'is_featured' => true,
            ],
        ];
    }

    private function getClinicServices(): array
    {
        return [
            [
                'name' => 'إزالة الشعر بالليزر',
                'description' => 'إزالة الشعر الدائمة باستخدام أحدث تقنيات الليزر',
                'category' => 'إزالة الشعر',
                'price' => 300,
                'duration' => 60,
                'is_featured' => true,
            ],
            [
                'name' => 'حقن البوتوكس',
                'description' => 'حقن البوتوكس لإزالة التجاعيد وعلامات التقدم في السن',
                'category' => 'حقن التجميل',
                'price' => 800,
                'duration' => 45,
                'is_featured' => true,
            ],
            [
                'name' => 'حقن الفيلر',
                'description' => 'حقن الفيلر لنفخ الشفاه وتحديد ملامح الوجه',
                'category' => 'حقن التجميل',
                'price' => 600,
                'duration' => 30,
                'is_featured' => true,
            ],
            [
                'name' => 'تقشير كيميائي للوجه',
                'description' => 'تقشير كيميائي لتجديد خلايا البشرة وتوحيد لونها',
                'category' => 'عناية بالبشرة',
                'price' => 400,
                'duration' => 90,
                'is_featured' => true,
            ],
            [
                'name' => 'علاج الندبات بالليزر',
                'description' => 'علاج الندبات وآثار الحبوب باستخدام الليزر',
                'category' => 'علاج طبي',
                'price' => 500,
                'duration' => 75,
                'is_featured' => false,
            ],
            [
                'name' => 'شد الوجه بالخيوط',
                'description' => 'شد الوجه غير الجراحي باستخدام الخيوط الطبية',
                'category' => 'شد وتجميل',
                'price' => 1200,
                'duration' => 120,
                'is_featured' => true,
            ],
        ];
    }
}
