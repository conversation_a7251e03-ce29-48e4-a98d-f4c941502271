<?php

namespace Database\Seeders;

use <PERSON><PERSON>ble\Base\Supports\BaseSeeder;
use <PERSON>tble\Ecommerce\Models\Brand;
use Bo<PERSON>ble\Slug\Facades\SlugHelper;

class BrandSeeder extends BaseSeeder
{
    public function run(): void
    {
        $this->uploadFiles('brands');

        $brands = [
            [
                'logo' => 'brands/1.png',
                'name' => 'FoodPound',
                'description' => 'New Snacks Release',
            ],
            [
                'logo' => 'brands/2.png',
                'name' => 'iTea JSC',
                'description' => 'Happy Tea 100% Organic. From $29.9',
            ],
            [
                'logo' => 'brands/3.png',
                'name' => 'Soda Brand',
                'description' => 'Fresh Meat Sausage. BUY 2 GET 1!',
            ],
            [
                'logo' => 'brands/4.png',
                'name' => 'Farmart',
                'description' => 'Fresh Meat Sausage. BUY 2 GET 1!',
            ],
            [
                'logo' => 'brands/3.png',
                'name' => 'Soda Brand',
                'description' => 'Fresh Meat Sausage. BUY 2 GET 1!',
            ],
        ];

        Brand::query()->truncate();

        foreach ($brands as $key => $item) {
            $item['order'] = $key;
            $item['is_featured'] = true;
            $brand = Brand::query()->create($item);

            SlugHelper::createSlug($brand);
        }
    }
}
