<?php

namespace Database\Seeders;

use <PERSON><PERSON><PERSON>\ACL\Database\Seeders\UserSeeder;
use <PERSON><PERSON>ble\Base\Supports\BaseSeeder;
use Botble\Contact\Database\Seeders\ContactSeeder;
use Botble\Ecommerce\Database\Seeders\CurrencySeeder;
use Bo<PERSON>ble\Ecommerce\Database\Seeders\ReviewSeeder;
use Bo<PERSON>ble\Ecommerce\Database\Seeders\ShippingSeeder;
use Botble\Ecommerce\Database\Seeders\TaxSeeder;
use Botble\Language\Database\Seeders\LanguageSeeder;

class DatabaseSeeder extends BaseSeeder
{
    public function run(): void
    {
        $this->prepareRun();

        $this->call([
            UserSeeder::class,
            LanguageSeeder::class,
            FaqSeeder::class,
            BrandSeeder::class,
            CurrencySeeder::class,
            ProductCategorySeeder::class,
            ProductCollectionSeeder::class,
            ProductLabelSeeder::class,
            ProductAttributeSeeder::class,
            ProductOptionSeeder::class,
            CustomerSeeder::class,
            TaxSeeder::class,
            ProductTagSeeder::class,
            ShippingSeeder::class,
            ProductSeeder::class,
            FlashSaleSeeder::class,
            ReviewSeeder::class,
            StoreLocatorSeeder::class,
            MarketplaceSeeder::class,
            EgyptBeautyStoresSeeder::class, // إضافة متاجر الجمال المصرية
            BeautyServicesSeeder::class, // إضافة خدمات الجمال للمتاجر
            BeautyProductsSeeder::class, // إضافة منتجات الجمال للمتاجر
            ContactSeeder::class,
            BlogSeeder::class,
            SimpleSliderSeeder::class,
            PageSeeder::class,
            AdsSeeder::class,
            SettingSeeder::class,
            MenuSeeder::class,
            ThemeOptionSeeder::class,
            WidgetSeeder::class,
        ]);

        $this->finished();
    }
}
