<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Botble\ACL\Models\Permission;
use Bo<PERSON>ble\ACL\Models\Role;

class JobApplicationPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء الأذونات الخاصة بخدمة وظفني
        $permissions = [
            [
                'name' => 'عرض طلبات التوظيف',
                'slug' => 'job-applications.index',
                'is_feature' => true,
            ],
            [
                'name' => 'عرض تفاصيل طلب التوظيف',
                'slug' => 'job-applications.show',
                'is_feature' => false,
            ],
            [
                'name' => 'تعديل طلب التوظيف',
                'slug' => 'job-applications.edit',
                'is_feature' => false,
            ],
            [
                'name' => 'حذف طلب التوظيف',
                'slug' => 'job-applications.destroy',
                'is_feature' => false,
            ],
            [
                'name' => 'ترشيح المرشحين للفيندورز',
                'slug' => 'job-applications.recommend',
                'is_feature' => false,
            ],
            [
                'name' => 'عرض إحصائيات طلبات التوظيف',
                'slug' => 'job-applications.statistics',
                'is_feature' => false,
            ],
            [
                'name' => 'إدارة فئات الوظائف',
                'slug' => 'job-categories.index',
                'is_feature' => true,
            ],
            [
                'name' => 'إنشاء فئة وظيفة',
                'slug' => 'job-categories.create',
                'is_feature' => false,
            ],
            [
                'name' => 'تعديل فئة وظيفة',
                'slug' => 'job-categories.edit',
                'is_feature' => false,
            ],
            [
                'name' => 'حذف فئة وظيفة',
                'slug' => 'job-categories.destroy',
                'is_feature' => false,
            ],
            [
                'name' => 'إدارة مهارات الوظائف',
                'slug' => 'job-skills.index',
                'is_feature' => true,
            ],
            [
                'name' => 'إنشاء مهارة وظيفة',
                'slug' => 'job-skills.create',
                'is_feature' => false,
            ],
            [
                'name' => 'تعديل مهارة وظيفة',
                'slug' => 'job-skills.edit',
                'is_feature' => false,
            ],
            [
                'name' => 'حذف مهارة وظيفة',
                'slug' => 'job-skills.destroy',
                'is_feature' => false,
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['slug' => $permission['slug']],
                [
                    'name' => $permission['name'],
                    'is_feature' => $permission['is_feature'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }

        // إضافة الأذونات لدور المدير العام (Super Admin)
        $superAdminRole = Role::where('slug', 'super-admin')->first();
        if ($superAdminRole) {
            $permissionIds = Permission::whereIn('slug', array_column($permissions, 'slug'))->pluck('id');
            $superAdminRole->permissions()->syncWithoutDetaching($permissionIds);
        }

        // إضافة الأذونات لدور الأدمن
        $adminRole = Role::where('slug', 'admin')->first();
        if ($adminRole) {
            $adminPermissions = [
                'job-applications.index',
                'job-applications.show',
                'job-applications.edit',
                'job-applications.recommend',
                'job-applications.statistics',
                'job-categories.index',
                'job-categories.create',
                'job-categories.edit',
                'job-skills.index',
                'job-skills.create',
                'job-skills.edit',
            ];
            
            $permissionIds = Permission::whereIn('slug', $adminPermissions)->pluck('id');
            $adminRole->permissions()->syncWithoutDetaching($permissionIds);
        }

        // إضافة الأذونات لدور مدير المحتوى
        $editorRole = Role::where('slug', 'editor')->first();
        if ($editorRole) {
            $editorPermissions = [
                'job-applications.index',
                'job-applications.show',
                'job-applications.edit',
                'job-applications.statistics',
            ];
            
            $permissionIds = Permission::whereIn('slug', $editorPermissions)->pluck('id');
            $editorRole->permissions()->syncWithoutDetaching($permissionIds);
        }

        $this->command->info('تم إنشاء أذونات خدمة وظفني بنجاح!');
    }
}
