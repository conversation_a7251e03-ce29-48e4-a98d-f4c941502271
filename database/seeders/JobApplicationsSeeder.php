<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class JobApplicationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 بدء تشغيل بيانات خدمة وظفني...');

        // تشغيل فئات الوظائف
        $this->call(JobCategoriesSeeder::class);

        // تشغيل مهارات الوظائف
        $this->call(JobSkillsSeeder::class);

        $this->command->info('✅ تم تشغيل جميع بيانات خدمة وظفني بنجاح!');
        $this->command->info('');
        $this->command->info('📋 ملخص ما تم إنشاؤه:');
        $this->command->info('   • فئات الوظائف (10 فئات)');
        $this->command->info('   • مهارات الوظائف (50+ مهارة)');
        $this->command->info('');
        $this->command->info('🎯 الخطوات التالية:');
        $this->command->info('   1. إضافة خدمة وظفني لقائمة الأدمن');
        $this->command->info('   2. إضافة خدمة وظفني لقائمة العملاء');
        $this->command->info('   3. إضافة خدمة وظفني لقائمة الفيندورز');
        $this->command->info('   4. اختبار النظام');
    }
}
