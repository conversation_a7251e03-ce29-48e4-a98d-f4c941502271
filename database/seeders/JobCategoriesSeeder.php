<?php

namespace Database\Seeders;

use App\Models\JobCategory;
use Illuminate\Database\Seeder;

class JobCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Beauty & Wellness',
                'name_ar' => 'الجمال والعافية',
                'description' => 'Jobs in beauty salons, spas, and wellness centers',
                'description_ar' => 'وظائف في صالونات التجميل والمنتجعات الصحية ومراكز العافية',
                'icon' => 'fas fa-spa',
                'color' => '#e91e63',
                'sort_order' => 1,
            ],
            [
                'name' => 'Healthcare',
                'name_ar' => 'الرعاية الصحية',
                'description' => 'Medical and healthcare positions',
                'description_ar' => 'المناصب الطبية والرعاية الصحية',
                'icon' => 'fas fa-heartbeat',
                'color' => '#4caf50',
                'sort_order' => 2,
            ],
            [
                'name' => 'Fitness & Sports',
                'name_ar' => 'اللياقة البدنية والرياضة',
                'description' => 'Gym trainers, fitness coaches, and sports professionals',
                'description_ar' => 'مدربي الصالات الرياضية ومدربي اللياقة البدنية والمهنيين الرياضيين',
                'icon' => 'fas fa-dumbbell',
                'color' => '#ff9800',
                'sort_order' => 3,
            ],
            [
                'name' => 'Customer Service',
                'name_ar' => 'خدمة العملاء',
                'description' => 'Reception, customer support, and service roles',
                'description_ar' => 'الاستقبال ودعم العملاء وأدوار الخدمة',
                'icon' => 'fas fa-headset',
                'color' => '#2196f3',
                'sort_order' => 4,
            ],
            [
                'name' => 'Sales & Marketing',
                'name_ar' => 'المبيعات والتسويق',
                'description' => 'Sales representatives, marketing specialists',
                'description_ar' => 'مندوبي المبيعات وأخصائيي التسويق',
                'icon' => 'fas fa-chart-line',
                'color' => '#9c27b0',
                'sort_order' => 5,
            ],
            [
                'name' => 'Administration',
                'name_ar' => 'الإدارة',
                'description' => 'Administrative and office management positions',
                'description_ar' => 'المناصب الإدارية وإدارة المكاتب',
                'icon' => 'fas fa-briefcase',
                'color' => '#607d8b',
                'sort_order' => 6,
            ],
            [
                'name' => 'Technical Support',
                'name_ar' => 'الدعم الفني',
                'description' => 'IT support, equipment maintenance, technical roles',
                'description_ar' => 'دعم تقنية المعلومات وصيانة المعدات والأدوار التقنية',
                'icon' => 'fas fa-tools',
                'color' => '#795548',
                'sort_order' => 7,
            ],
            [
                'name' => 'Cleaning & Maintenance',
                'name_ar' => 'التنظيف والصيانة',
                'description' => 'Cleaning staff, maintenance workers',
                'description_ar' => 'موظفي التنظيف وعمال الصيانة',
                'icon' => 'fas fa-broom',
                'color' => '#009688',
                'sort_order' => 8,
            ],
            [
                'name' => 'Security',
                'name_ar' => 'الأمن',
                'description' => 'Security guards, safety personnel',
                'description_ar' => 'حراس الأمن وموظفي السلامة',
                'icon' => 'fas fa-shield-alt',
                'color' => '#f44336',
                'sort_order' => 9,
            ],
            [
                'name' => 'Other',
                'name_ar' => 'أخرى',
                'description' => 'Other job categories not listed above',
                'description_ar' => 'فئات وظائف أخرى غير مدرجة أعلاه',
                'icon' => 'fas fa-ellipsis-h',
                'color' => '#9e9e9e',
                'sort_order' => 10,
            ],
        ];

        foreach ($categories as $category) {
            JobCategory::firstOrCreate(
                ['name' => $category['name']],
                $category
            );
        }

        $this->command->info('تم إنشاء فئات الوظائف بنجاح!');
    }
}
