<?php

namespace Database\Seeders;

use App\Models\JobSkill;
use Illuminate\Database\Seeder;

class JobSkillsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $skills = [
            // Technical Skills
            [
                'name' => 'Microsoft Office',
                'name_ar' => 'مايكروسوفت أوفيس',
                'category' => 'technical',
            ],
            [
                'name' => 'Computer Skills',
                'name_ar' => 'مهارات الحاسوب',
                'category' => 'technical',
            ],
            [
                'name' => 'Social Media Management',
                'name_ar' => 'إدارة وسائل التواصل الاجتماعي',
                'category' => 'technical',
            ],
            [
                'name' => 'Data Entry',
                'name_ar' => 'إدخال البيانات',
                'category' => 'technical',
            ],
            [
                'name' => 'Point of Sale (POS) Systems',
                'name_ar' => 'أنظمة نقاط البيع',
                'category' => 'technical',
            ],

            // Language Skills
            [
                'name' => 'Arabic',
                'name_ar' => 'العربية',
                'category' => 'language',
            ],
            [
                'name' => 'English',
                'name_ar' => 'الإنجليزية',
                'category' => 'language',
            ],
            [
                'name' => 'French',
                'name_ar' => 'الفرنسية',
                'category' => 'language',
            ],
            [
                'name' => 'Spanish',
                'name_ar' => 'الإسبانية',
                'category' => 'language',
            ],

            // Administrative Skills
            [
                'name' => 'Customer Service',
                'name_ar' => 'خدمة العملاء',
                'category' => 'administrative',
            ],
            [
                'name' => 'Reception',
                'name_ar' => 'الاستقبال',
                'category' => 'administrative',
            ],
            [
                'name' => 'Appointment Scheduling',
                'name_ar' => 'جدولة المواعيد',
                'category' => 'administrative',
            ],
            [
                'name' => 'Phone Handling',
                'name_ar' => 'التعامل مع الهاتف',
                'category' => 'administrative',
            ],
            [
                'name' => 'Filing & Documentation',
                'name_ar' => 'الأرشفة والتوثيق',
                'category' => 'administrative',
            ],
            [
                'name' => 'Inventory Management',
                'name_ar' => 'إدارة المخزون',
                'category' => 'administrative',
            ],

            // Sales Skills
            [
                'name' => 'Sales Techniques',
                'name_ar' => 'تقنيات البيع',
                'category' => 'sales',
            ],
            [
                'name' => 'Product Knowledge',
                'name_ar' => 'معرفة المنتجات',
                'category' => 'sales',
            ],
            [
                'name' => 'Upselling',
                'name_ar' => 'البيع الإضافي',
                'category' => 'sales',
            ],
            [
                'name' => 'Customer Relationship Management',
                'name_ar' => 'إدارة علاقات العملاء',
                'category' => 'sales',
            ],

            // Beauty & Wellness Skills
            [
                'name' => 'Hair Cutting',
                'name_ar' => 'قص الشعر',
                'category' => 'beauty',
            ],
            [
                'name' => 'Hair Styling',
                'name_ar' => 'تصفيف الشعر',
                'category' => 'beauty',
            ],
            [
                'name' => 'Hair Coloring',
                'name_ar' => 'صبغ الشعر',
                'category' => 'beauty',
            ],
            [
                'name' => 'Makeup Application',
                'name_ar' => 'تطبيق المكياج',
                'category' => 'beauty',
            ],
            [
                'name' => 'Nail Care',
                'name_ar' => 'العناية بالأظافر',
                'category' => 'beauty',
            ],
            [
                'name' => 'Facial Treatments',
                'name_ar' => 'علاجات الوجه',
                'category' => 'beauty',
            ],
            [
                'name' => 'Massage Therapy',
                'name_ar' => 'العلاج بالتدليك',
                'category' => 'beauty',
            ],
            [
                'name' => 'Skin Care',
                'name_ar' => 'العناية بالبشرة',
                'category' => 'beauty',
            ],

            // Fitness Skills
            [
                'name' => 'Personal Training',
                'name_ar' => 'التدريب الشخصي',
                'category' => 'fitness',
            ],
            [
                'name' => 'Group Fitness',
                'name_ar' => 'اللياقة الجماعية',
                'category' => 'fitness',
            ],
            [
                'name' => 'Yoga Instruction',
                'name_ar' => 'تعليم اليوغا',
                'category' => 'fitness',
            ],
            [
                'name' => 'Nutrition Counseling',
                'name_ar' => 'الاستشارة الغذائية',
                'category' => 'fitness',
            ],
            [
                'name' => 'Equipment Maintenance',
                'name_ar' => 'صيانة المعدات',
                'category' => 'fitness',
            ],

            // Healthcare Skills
            [
                'name' => 'First Aid',
                'name_ar' => 'الإسعافات الأولية',
                'category' => 'healthcare',
            ],
            [
                'name' => 'CPR Certification',
                'name_ar' => 'شهادة الإنعاش القلبي الرئوي',
                'category' => 'healthcare',
            ],
            [
                'name' => 'Medical Equipment Operation',
                'name_ar' => 'تشغيل المعدات الطبية',
                'category' => 'healthcare',
            ],
            [
                'name' => 'Patient Care',
                'name_ar' => 'رعاية المرضى',
                'category' => 'healthcare',
            ],

            // Creative Skills
            [
                'name' => 'Photography',
                'name_ar' => 'التصوير الفوتوغرافي',
                'category' => 'creative',
            ],
            [
                'name' => 'Graphic Design',
                'name_ar' => 'التصميم الجرافيكي',
                'category' => 'creative',
            ],
            [
                'name' => 'Content Creation',
                'name_ar' => 'إنشاء المحتوى',
                'category' => 'creative',
            ],
            [
                'name' => 'Video Editing',
                'name_ar' => 'تحرير الفيديو',
                'category' => 'creative',
            ],

            // Other Skills
            [
                'name' => 'Time Management',
                'name_ar' => 'إدارة الوقت',
                'category' => 'other',
            ],
            [
                'name' => 'Problem Solving',
                'name_ar' => 'حل المشكلات',
                'category' => 'other',
            ],
            [
                'name' => 'Teamwork',
                'name_ar' => 'العمل الجماعي',
                'category' => 'other',
            ],
            [
                'name' => 'Leadership',
                'name_ar' => 'القيادة',
                'category' => 'other',
            ],
            [
                'name' => 'Communication',
                'name_ar' => 'التواصل',
                'category' => 'other',
            ],
            [
                'name' => 'Multitasking',
                'name_ar' => 'تعدد المهام',
                'category' => 'other',
            ],
            [
                'name' => 'Attention to Detail',
                'name_ar' => 'الانتباه للتفاصيل',
                'category' => 'other',
            ],
            [
                'name' => 'Flexibility',
                'name_ar' => 'المرونة',
                'category' => 'other',
            ],
        ];

        foreach ($skills as $skill) {
            JobSkill::firstOrCreate(
                ['name' => $skill['name']],
                $skill
            );
        }

        $this->command->info('تم إنشاء مهارات الوظائف بنجاح!');
    }
}
