<?php

namespace Database\Seeders;

use <PERSON><PERSON><PERSON>\Base\Supports\BaseSeeder;
use <PERSON><PERSON>ble\Blog\Models\Category;
use Bo<PERSON>ble\Blog\Models\Post;
use <PERSON><PERSON><PERSON>\Setting\Facades\Setting;
use Bo<PERSON><PERSON>\Slug\Facades\SlugHelper;
use Bo<PERSON>ble\Slug\Models\Slug;
use Bo<PERSON>ble\Theme\Facades\Theme;

class SettingSeeder extends BaseSeeder
{
    public function run(): void
    {
        $settings = [
            'show_admin_bar' => '1',
            'theme' => Theme::getThemeName(),
            'media_random_hash' => md5((string) time()),
            'admin_favicon' => 'general/favicon.png',
            'admin_logo' => 'general/logo-light.png',
            SlugHelper::getPermalinkSettingKey(Post::class) => 'blog',
            SlugHelper::getPermalinkSettingKey(Category::class) => 'blog',
            'payment_cod_status' => 1,
            'payment_cod_description' => 'Please pay money directly to the postman, if you choose cash on delivery method (COD).',
            'payment_bank_transfer_status' => 1,
            'payment_bank_transfer_description' => 'Please send money to our bank account: ACB - 69270 213 19.',
            'payment_stripe_payment_type' => 'stripe_checkout',
            'plugins_ecommerce_customer_new_order_status' => 0,
            'plugins_ecommerce_admin_new_order_status' => 0,
            'ecommerce_is_enabled_support_digital_products' => 1,
            'ecommerce_load_countries_states_cities_from_location_plugin' => 0,
            'payment_bank_transfer_display_bank_info_at_the_checkout_success_page' => 1,
            'ecommerce_product_sku_format' => 'FM-2443-%s%s%s%s',
            'language_switcher_display' => 'dropdown',
        ];

        Setting::delete(array_keys($settings));

        Setting::set($settings)->save();

        Slug::query()->where('reference_type', Post::class)->update(['prefix' => 'blog']);
        Slug::query()->where('reference_type', Category::class)->update(['prefix' => 'blog']);
    }
}
