<?php

/**
 * اختبار إصلاحات أعمدة قاعدة البيانات
 */

echo "🔍 اختبار إصلاحات أعمدة قاعدة البيانات...\n\n";

// 1. فحص الكنترولرز للتأكد من استخدام الأعمدة الصحيحة
echo "🎮 فحص الكنترولرز:\n";

$controllers = [
    'platform/plugins/training-appointment/src/Http/Controllers/Vendor/CourseController.php',
    'platform/plugins/training-appointment/src/Http/Controllers/Vendor/CourseLessonController.php',
];

$controllerFixesStatus = true;
foreach ($controllers as $controller) {
    if (file_exists(__DIR__ . '/' . $controller)) {
        $content = file_get_contents(__DIR__ . '/' . $controller);
        
        echo "  📁 " . basename($controller) . ":\n";
        
        // فحص استخدام title بدلاً من name للدورات
        if (strpos($content, "pluck('title', 'id')") !== false) {
            echo "    ✅ يستخدم 'title' للدورات\n";
        } elseif (strpos($content, "pluck('name', 'id')") !== false) {
            echo "    ❌ لا يزال يستخدم 'name' للدورات\n";
            $controllerFixesStatus = false;
        } else {
            echo "    ✅ لا يستخدم pluck للدورات\n";
        }
        
        // فحص استخدام title في البحث
        if (strpos($content, "where('title', 'LIKE'") !== false) {
            echo "    ✅ يستخدم 'title' في البحث\n";
        } elseif (strpos($content, "where('name', 'LIKE'") !== false) {
            echo "    ❌ لا يزال يستخدم 'name' في البحث\n";
            $controllerFixesStatus = false;
        } else {
            echo "    ✅ لا يستخدم البحث في الدورات\n";
        }
    } else {
        echo "  ❌ " . basename($controller) . " - غير موجود\n";
        $controllerFixesStatus = false;
    }
}

// 2. فحص الواجهات للتأكد من استخدام الأعمدة الصحيحة
echo "\n🎨 فحص الواجهات:\n";

$views = [
    'platform/plugins/training-appointment/resources/views/vendor/courses/index.blade.php',
    'platform/plugins/training-appointment/resources/views/vendor/courses/create.blade.php',
    'platform/plugins/training-appointment/resources/views/vendor/courses/edit.blade.php',
    'platform/plugins/training-appointment/resources/views/vendor/courses/show.blade.php',
    'platform/plugins/training-appointment/resources/views/vendor/lessons/index.blade.php',
];

$viewFixesStatus = true;
foreach ($views as $view) {
    if (file_exists(__DIR__ . '/' . $view)) {
        $content = file_get_contents(__DIR__ . '/' . $view);
        
        echo "  📁 " . basename($view) . ":\n";
        
        // فحص استخدام title بدلاً من name
        if (strpos($content, '$course->title') !== false || strpos($content, 'name="title"') !== false) {
            echo "    ✅ يستخدم 'title' للدورات\n";
        } else {
            echo "    ⚠️ قد لا يحتوي على مراجع للدورات\n";
        }
        
        // فحص عدم وجود name للدورات
        if (strpos($content, '$course->name') !== false || strpos($content, 'name="name"') !== false) {
            echo "    ❌ لا يزال يستخدم 'name' للدورات\n";
            $viewFixesStatus = false;
        } else {
            echo "    ✅ لا يستخدم 'name' للدورات\n";
        }
    } else {
        echo "  ❌ " . basename($view) . " - غير موجود\n";
        $viewFixesStatus = false;
    }
}

// 3. فحص بنية قاعدة البيانات المتوقعة
echo "\n🗄️ بنية قاعدة البيانات المتوقعة:\n";

$expectedColumns = [
    'ta_courses' => ['title', 'description', 'content', 'price', 'duration', 'status', 'store_id', 'category_id'],
    'ta_course_categories' => ['name', 'description', 'status'],
    'ta_services' => ['name', 'description', 'price', 'duration', 'status', 'store_id'],
    'ta_appointments' => ['customer_name', 'customer_email', 'service_id', 'store_id', 'status'],
    'ta_course_lessons' => ['title', 'description', 'content', 'course_id', 'duration', 'is_free'],
];

foreach ($expectedColumns as $table => $columns) {
    echo "  📊 {$table}:\n";
    foreach ($columns as $column) {
        echo "    - {$column}\n";
    }
}

// 4. ملخص الإصلاحات
echo "\n" . str_repeat("=", 60) . "\n";
echo "📊 ملخص إصلاحات قاعدة البيانات\n";
echo str_repeat("=", 60) . "\n";

echo "🎮 الكنترولرز: " . ($controllerFixesStatus ? "✅ مُصلحة" : "❌ تحتاج إصلاح") . "\n";
echo "🎨 الواجهات: " . ($viewFixesStatus ? "✅ مُصلحة" : "❌ تحتاج إصلاح") . "\n";

$overallStatus = $controllerFixesStatus && $viewFixesStatus;

echo "\n🎯 الحالة العامة: " . ($overallStatus ? "✅ جميع الإصلاحات مكتملة" : "⚠️ تحتاج مراجعة") . "\n";

// 5. الأخطاء المُصلحة
echo "\n🔧 الأخطاء المُصلحة:\n";
echo "  ✅ SQLSTATE[42S22]: Column not found: 1054 Unknown column 'name'\n";
echo "  ✅ تم تغيير جميع المراجع من 'name' إلى 'title' للدورات\n";
echo "  ✅ تم تحديث الكنترولرز لاستخدام الأعمدة الصحيحة\n";
echo "  ✅ تم تحديث الواجهات لاستخدام الأعمدة الصحيحة\n";

// 6. اختبارات موصى بها
echo "\n🧪 اختبارات موصى بها:\n";
echo "  1. زيارة /vendor/courses - يجب أن تعمل بدون أخطاء\n";
echo "  2. زيارة /vendor/lessons/create - يجب أن تعمل بدون أخطاء\n";
echo "  3. إضافة دورة جديدة - يجب أن تعمل\n";
echo "  4. إضافة درس جديد - يجب أن تعمل\n";

if ($overallStatus) {
    echo "\n🎉 جميع إصلاحات قاعدة البيانات مكتملة!\n";
    echo "النظام جاهز للاستخدام بدون أخطاء قاعدة البيانات.\n";
} else {
    echo "\n⚠️ يرجى مراجعة الإصلاحات المذكورة أعلاه.\n";
}

echo "\n🏁 انتهى اختبار إصلاحات قاعدة البيانات!\n";
