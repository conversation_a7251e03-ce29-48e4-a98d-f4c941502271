<?php

require_once 'vendor/autoload.php';

// بدء Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 === فحص مفصل لاستجابات APIs ===\n\n";

// إنشاء token للاختبار
$testToken = null;
try {
    $testUser = \Botble\ACL\Models\User::where('email', '<EMAIL>')->first();
    if ($testUser) {
        $testToken = $testUser->createToken('debug-device')->plainTextToken;
        echo "✅ تم إنشاء token للاختبار\n\n";
    }
} catch (Exception $e) {
    echo "❌ فشل في إنشاء token: {$e->getMessage()}\n\n";
}

// قائمة APIs للاختبار المفصل
$apisToTest = [
    // APIs المصادقة
    'auth' => [
        'login' => ['method' => 'POST', 'data' => ['email' => '<EMAIL>', 'password' => 'password123', 'device_name' => 'test', 'device_type' => 'android']],
        'me' => ['method' => 'GET', 'auth' => true],
        'logout' => ['method' => 'POST', 'auth' => true],
    ],
    
    // APIs العامة
    'public' => [
        'config' => ['method' => 'GET'],
        'home' => ['method' => 'GET'],
        'search' => ['method' => 'GET', 'params' => ['q' => 'test']],
        'stores' => ['method' => 'GET'],
        'categories' => ['method' => 'GET'],
        'products' => ['method' => 'GET'],
        'services' => ['method' => 'GET'],
        'courses' => ['method' => 'GET'],
        'reels' => ['method' => 'GET'],
    ],
    
    // APIs محمية
    'protected' => [
        'wishlist' => ['method' => 'GET', 'auth' => true],
        'cart/summary' => ['method' => 'GET', 'auth' => true, 'prefix' => 'ecommerce'],
        'notifications' => ['method' => 'GET', 'auth' => true],
        'preferences' => ['method' => 'GET', 'auth' => true],
    ],
    
    // APIs الريلز
    'reels' => [
        'feed' => ['method' => 'GET'],
        'trending' => ['method' => 'GET'],
    ],
];

function testApi($endpoint, $config, $testToken = null) {
    $method = $config['method'] ?? 'GET';
    $needsAuth = $config['auth'] ?? false;
    $data = $config['data'] ?? null;
    $params = $config['params'] ?? [];
    $prefix = $config['prefix'] ?? '';

    // بناء URL
    if ($prefix) {
        $url = "http://localhost:8000/api/v1/mobile/{$prefix}/{$endpoint}";
    } else {
        $url = "http://localhost:8000/api/v1/mobile/{$endpoint}";
    }

    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }

    // إعداد headers
    $headers = ["Accept: application/json"];
    if ($needsAuth && $testToken) {
        $headers[] = "Authorization: Bearer {$testToken}";
    }

    // إنشاء token جديد للاختبار إذا كان مطلوب
    if ($needsAuth && !$testToken) {
        try {
            $user = \Botble\ACL\Models\User::where('email', '<EMAIL>')->first();
            if ($user) {
                $testToken = $user->createToken('test-device')->plainTextToken;
                $headers[] = "Authorization: Bearer {$testToken}";
            }
        } catch (Exception $e) {
            echo "   ❌ فشل في إنشاء token: {$e->getMessage()}\n";
        }
    }
    
    // إعداد context
    $contextOptions = [
        'http' => [
            'method' => $method,
            'header' => implode("\r\n", $headers),
            'ignore_errors' => true
        ]
    ];
    
    if ($data && $method === 'POST') {
        $contextOptions['http']['content'] = json_encode($data);
        $contextOptions['http']['header'] .= "\r\nContent-Type: application/json";
    }
    
    $context = stream_context_create($contextOptions);
    
    echo "🔍 اختبار: {$endpoint}\n";
    echo "   URL: {$url}\n";
    echo "   Method: {$method}\n";
    echo "   Auth: " . ($needsAuth ? 'Required' : 'Not Required') . "\n";
    
    try {
        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            echo "   ❌ فشل في الاتصال\n";
            return false;
        }
        
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "   ❌ استجابة ليست JSON صحيح\n";
            echo "   📄 الاستجابة: " . substr($response, 0, 200) . "...\n";
            return false;
        }
        
        echo "   📊 البيانات المستلمة:\n";
        echo "   " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
        // تحليل الاستجابة
        if (isset($data['success']) && $data['success']) {
            echo "   ✅ نجح (success: true)\n";
            return true;
        } elseif (isset($data['error']) && $data['error'] === false) {
            echo "   ✅ نجح (error: false)\n";
            return true;
        } elseif (isset($data['data'])) {
            echo "   ✅ نجح (يحتوي على data)\n";
            return true;
        } else {
            echo "   ⚠️ استجابة غير متوقعة\n";
            return false;
        }
        
    } catch (Exception $e) {
        echo "   ❌ خطأ: {$e->getMessage()}\n";
        return false;
    }
    
    echo "\n";
}

// تشغيل الاختبارات
foreach ($apisToTest as $category => $apis) {
    echo "📂 === فئة: {$category} ===\n";
    
    foreach ($apis as $endpoint => $config) {
        if ($category === 'auth') {
            $fullEndpoint = "auth/{$endpoint}";
        } elseif ($category === 'public') {
            $fullEndpoint = "public/{$endpoint}";
        } elseif ($category === 'protected') {
            $fullEndpoint = $endpoint;
        } elseif ($category === 'reels') {
            $fullEndpoint = "reels/{$endpoint}";
        }
        
        testApi($fullEndpoint, $config, $testToken);
    }
    
    echo "\n";
}

echo "🎯 === انتهاء الفحص المفصل ===\n";
