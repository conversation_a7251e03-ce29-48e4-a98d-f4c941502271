<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Botble\TrainingAppointment\Models\Course;
use Botble\TrainingAppointment\Models\CourseCategory;
use Illuminate\Http\Request;
use Botble\Theme\Facades\Theme;

echo "=== Simulating Controller Action ===\n";

// Simulate the exact controller logic
$slug = 'Test-Category';

try {
    $category = CourseCategory::query()
        ->where('slug', $slug)
        ->where('status', 'published')
        ->firstOrFail();
    
    echo "Category found: " . $category->name . "\n";
    
    $courses = Course::query()
        ->where('category_id', $category->id)
        ->where('status', 'published')
        ->with(['category', 'store'])
        ->paginate(12);
    
    echo "Courses found: " . $courses->count() . "\n";
    
    // Check each course for problematic data
    foreach ($courses as $course) {
        echo "\nChecking course: " . $course->id . "\n";
        
        // Check all the fields used in the view
        $fieldsToCheck = [
            'title' => $course->title,
            'description' => $course->description,
            'category.name' => $course->category ? $course->category->name : null,
            'store.name' => $course->store ? $course->store->name : null,
        ];
        
        foreach ($fieldsToCheck as $fieldName => $value) {
            if ($value !== null) {
                $type = gettype($value);
                echo "  $fieldName type: $type\n";
                
                if (is_array($value)) {
                    echo "  *** FOUND ARRAY IN $fieldName: " . json_encode($value) . " ***\n";
                }
                
                // Test htmlspecialchars on this value
                try {
                    htmlspecialchars($value);
                    echo "  $fieldName htmlspecialchars: OK\n";
                } catch (TypeError $e) {
                    echo "  *** $fieldName htmlspecialchars: ERROR - " . $e->getMessage() . " ***\n";
                    echo "  Value type: " . gettype($value) . "\n";
                    if (is_array($value)) {
                        echo "  Array contents: " . json_encode($value) . "\n";
                    }
                }
            }
        }
    }
    
    // Also check category fields
    echo "\nChecking category fields:\n";
    $categoryFields = [
        'name' => $category->name,
        'description' => $category->description,
    ];
    
    foreach ($categoryFields as $fieldName => $value) {
        if ($value !== null) {
            $type = gettype($value);
            echo "  category.$fieldName type: $type\n";
            
            if (is_array($value)) {
                echo "  *** FOUND ARRAY IN category.$fieldName: " . json_encode($value) . " ***\n";
            }
            
            try {
                htmlspecialchars($value);
                echo "  category.$fieldName htmlspecialchars: OK\n";
            } catch (TypeError $e) {
                echo "  *** category.$fieldName htmlspecialchars: ERROR - " . $e->getMessage() . " ***\n";
                echo "  Value type: " . gettype($value) . "\n";
                if (is_array($value)) {
                    echo "  Array contents: " . json_encode($value) . "\n";
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

// Let's also check if there are any courses with array data
echo "\n=== Checking all courses for array data ===\n";

$allCourses = Course::with(['category', 'store'])->take(5)->get();

foreach ($allCourses as $course) {
    echo "\nCourse ID: " . $course->id . "\n";
    
    $fieldsToCheck = [
        'title' => $course->title,
        'description' => $course->description,
        'content' => $course->content,
    ];
    
    foreach ($fieldsToCheck as $fieldName => $value) {
        $type = gettype($value);
        if (is_array($value)) {
            echo "  *** FOUND ARRAY IN $fieldName: " . json_encode($value) . " ***\n";
        }
    }
    
    if ($course->category) {
        if (is_array($course->category->name)) {
            echo "  *** FOUND ARRAY IN category.name: " . json_encode($course->category->name) . " ***\n";
        }
        if (is_array($course->category->description)) {
            echo "  *** FOUND ARRAY IN category.description: " . json_encode($course->category->description) . " ***\n";
        }
    }
    
    if ($course->store) {
        if (is_array($course->store->name)) {
            echo "  *** FOUND ARRAY IN store.name: " . json_encode($course->store->name) . " ***\n";
        }
    }
}