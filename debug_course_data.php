<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Botble\TrainingAppointment\Models\Course;
use Botble\TrainingAppointment\Models\CourseCategory;

echo "=== Debugging Course Data Types ===\n";

// Get a course from the database
$course = Course::with(['category', 'store'])->first();

if ($course) {
    echo "Course found: " . $course->id . "\n";

    // Check each field type
    $fields = ['title', 'description', 'content'];

    foreach ($fields as $field) {
        $value = $course->$field;
        $type = gettype($value);
        echo "Course $field type: $type\n";

        if (is_array($value)) {
            echo "Course $field is an ARRAY: " . json_encode($value) . "\n";
        } else {
            echo "Course $field value: " . substr($value, 0, 50) . "...\n";
        }
    }

    // Check category
    if ($course->category) {
        $categoryFields = ['name', 'description'];

        foreach ($categoryFields as $field) {
            $value = $course->category->$field;
            $type = gettype($value);
            echo "Category $field type: $type\n";

            if (is_array($value)) {
                echo "Category $field is an ARRAY: " . json_encode($value) . "\n";
            } else {
                echo "Category $field value: " . substr($value, 0, 50) . "...\n";
            }
        }
    }
} else {
    echo "No courses found\n";
}

// Also check a specific category
$category = CourseCategory::first();

if ($category) {
    echo "\n=== Category Data ===\n";
    echo "Category found: " . $category->id . "\n";

    $categoryFields = ['name', 'description'];

    foreach ($categoryFields as $field) {
        $value = $category->$field;
        $type = gettype($value);
        echo "Category $field type: $type\n";

        if (is_array($value)) {
            echo "Category $field is an ARRAY: " . json_encode($value) . "\n";
        } else {
            echo "Category $field value: " . substr($value, 0, 50) . "...\n";
        }
    }
}

echo "\n=== Testing htmlspecialchars with array ===\n";
try {
    $testArray = ['test', 'array'];
    echo htmlspecialchars($testArray);
} catch (TypeError $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
