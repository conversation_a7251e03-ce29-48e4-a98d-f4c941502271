<?php

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== فحص الـ slugs للدورات ===\n";

// فحص الـ slugs للدورات
$slugs = \Botble\Slug\Models\Slug::where('reference_type', 'Botble\TrainingAppointment\Models\Course')->get();
echo 'Course Slugs found: ' . $slugs->count() . "\n";
foreach ($slugs as $slug) {
    echo 'Slug: ' . $slug->key . ' -> Course ID: ' . $slug->reference_id . ' | Prefix: ' . $slug->prefix . "\n";
}

echo "\n=== فحص الدورات الموجودة ===\n";
// فحص الدورات الموجودة
$courses = \Botble\TrainingAppointment\Models\Course::where('status', 'published')->get();
echo 'Published Courses found: ' . $courses->count() . "\n";
foreach ($courses as $course) {
    echo 'Course: ' . $course->title . ' | ID: ' . $course->id . "\n";
}

echo "\n=== اختبار slug محدد ===\n";
// اختبار slug محدد
$testSlug = 'facial-cleansing-facial-treatments';
echo "Testing slug: $testSlug\n";

$slugRecord = \Botble\Slug\Models\Slug::where('key', $testSlug)
    ->where('reference_type', 'Botble\TrainingAppointment\Models\Course')
    ->first();

if ($slugRecord) {
    echo "Slug record found!\n";
    echo "Reference ID: " . $slugRecord->reference_id . "\n";
    echo "Prefix: " . $slugRecord->prefix . "\n";
    
    $course = \Botble\TrainingAppointment\Models\Course::find($slugRecord->reference_id);
    if ($course) {
        echo "Course found: " . $course->title . "\n";
        echo "Course status: " . $course->status . "\n";
    } else {
        echo "Course not found with ID: " . $slugRecord->reference_id . "\n";
    }
} else {
    echo "Slug record NOT found!\n";
    
    // البحث في جميع الـ slugs
    echo "\nAll slugs containing 'facial':\n";
    $facialSlugs = \Botble\Slug\Models\Slug::where('key', 'like', '%facial%')->get();
    foreach ($facialSlugs as $slug) {
        echo "Found: " . $slug->key . " -> " . $slug->reference_type . " (ID: " . $slug->reference_id . ")\n";
    }
}

echo "\n=== فحص prefix للدورات ===\n";
$prefix = \Botble\Slug\Facades\SlugHelper::getPrefix(\Botble\TrainingAppointment\Models\Course::class);
echo "Course prefix: " . ($prefix ?: 'NULL') . "\n";
