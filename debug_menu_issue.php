<?php

require_once 'vendor/autoload.php';

// بدء Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== تشخيص مشكلة القائمة الجانبية ===\n\n";

// 1. فحص تفعيل الإضافة
echo "1. فحص تفعيل الإضافة:\n";
if (is_plugin_active('training-appointment')) {
    echo "✅ إضافة training-appointment مفعلة\n";
} else {
    echo "❌ إضافة training-appointment غير مفعلة\n";
    exit;
}

if (is_plugin_active('marketplace')) {
    echo "✅ إضافة marketplace مفعلة\n";
} else {
    echo "❌ إضافة marketplace غير مفعلة\n";
    exit;
}

// 2. فحص المسارات
echo "\n2. فحص المسارات:\n";
$routes = [
    'marketplace.vendor.services.index',
    'marketplace.vendor.appointments.index',
    'marketplace.vendor.courses.index'
];

foreach ($routes as $route) {
    try {
        $url = route($route);
        echo "✅ {$route}: {$url}\n";
    } catch (Exception $e) {
        echo "❌ {$route}: {$e->getMessage()}\n";
    }
}

// 3. فحص Service Providers
echo "\n3. فحص Service Providers:\n";

// فحص Marketplace Service Provider
$marketplaceFile = 'platform/plugins/marketplace/src/Providers/MarketplaceServiceProvider.php';
if (file_exists($marketplaceFile)) {
    echo "✅ Marketplace Service Provider موجود\n";
    
    $content = file_get_contents($marketplaceFile);
    if (strpos($content, "is_plugin_active('training-appointment')") !== false) {
        echo "✅ فحص تفعيل training-appointment موجود في Marketplace\n";
    } else {
        echo "❌ فحص تفعيل training-appointment مفقود في Marketplace\n";
    }
    
    if (strpos($content, 'marketplace.vendor.services') !== false) {
        echo "✅ عنصر الخدمات موجود في Marketplace\n";
    } else {
        echo "❌ عنصر الخدمات مفقود في Marketplace\n";
    }
} else {
    echo "❌ Marketplace Service Provider مفقود\n";
}

// 4. فحص تسجيل دخول البائع
echo "\n4. فحص تسجيل دخول البائع:\n";

$vendor = \Botble\Ecommerce\Models\Customer::where('is_vendor', true)->first();
if ($vendor) {
    echo "✅ يوجد بائع في النظام: {$vendor->name}\n";
    
    // محاكاة تسجيل الدخول
    auth('customer')->login($vendor);
    
    if (auth('customer')->check()) {
        echo "✅ تم تسجيل دخول البائع\n";
        
        if (auth('customer')->user()->is_vendor) {
            echo "✅ المستخدم هو بائع\n";
        } else {
            echo "❌ المستخدم ليس بائع\n";
        }
    } else {
        echo "❌ فشل تسجيل دخول البائع\n";
    }
} else {
    echo "❌ لا يوجد بائع في النظام\n";
}

// 5. فحص ملف القائمة
echo "\n5. فحص ملف القائمة:\n";

$menuFile = 'platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/menu.blade.php';
if (file_exists($menuFile)) {
    echo "✅ ملف القائمة موجود\n";
    
    $content = file_get_contents($menuFile);
    if (strpos($content, "DashboardMenu::getAll('vendor')") !== false) {
        echo "✅ الملف يستخدم قائمة البائع\n";
    } else {
        echo "❌ الملف لا يستخدم قائمة البائع\n";
    }
} else {
    echo "❌ ملف القائمة مفقود\n";
}

// 6. فحص الصفحة مباشرة
echo "\n6. فحص الصفحة مباشرة:\n";

try {
    $url = 'http://localhost:8000/vendor/dashboard';
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Debug Script'
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    
    if ($response) {
        echo "✅ الصفحة تحمل بشكل صحيح\n";
        echo "✅ حجم المحتوى: " . number_format(strlen($response)) . " بايت\n";
        
        // فحص وجود عناصر القائمة
        $searches = [
            'الدورات التدريبية' => 'عنصر الدورات',
            'الخدمات' => 'عنصر الخدمات',
            'المواعيد' => 'عنصر المواعيد',
            'ti ti-school' => 'أيقونة الدورات',
            'ti ti-briefcase' => 'أيقونة الخدمات',
            'ti ti-calendar-event' => 'أيقونة المواعيد',
            '/vendor/services' => 'رابط الخدمات',
            '/vendor/appointments' => 'رابط المواعيد',
            'DashboardMenu' => 'استدعاء DashboardMenu',
        ];
        
        foreach ($searches as $search => $description) {
            if (strpos($response, $search) !== false) {
                echo "✅ {$description}: موجود\n";
            } else {
                echo "❌ {$description}: مفقود\n";
            }
        }
        
        // حفظ جزء من HTML للفحص
        $menuStart = strpos($response, '<ul class="menu">');
        if ($menuStart !== false) {
            $menuEnd = strpos($response, '</ul>', $menuStart);
            if ($menuEnd !== false) {
                $menuHtml = substr($response, $menuStart, $menuEnd - $menuStart + 5);
                echo "\n--- محتوى القائمة ---\n";
                echo $menuHtml . "\n";
                echo "--- نهاية محتوى القائمة ---\n";
            }
        }
        
    } else {
        echo "❌ فشل في تحميل الصفحة\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص الصفحة: {$e->getMessage()}\n";
}

// 7. فحص الـ Cache
echo "\n7. فحص الـ Cache:\n";

try {
    \Illuminate\Support\Facades\Cache::flush();
    echo "✅ تم مسح الـ Cache\n";
} catch (Exception $e) {
    echo "❌ خطأ في مسح الـ Cache: {$e->getMessage()}\n";
}

// 8. التوصيات
echo "\n=== التوصيات ===\n";
echo "1. تأكد من أن السيرفر يعمل على http://localhost:8000\n";
echo "2. امسح جميع أنواع الـ Cache:\n";
echo "   - php artisan cache:clear\n";
echo "   - php artisan config:clear\n";
echo "   - php artisan view:clear\n";
echo "   - php artisan route:clear\n";
echo "3. أعد تشغيل السيرفر\n";
echo "4. تحقق من أن البائع مسجل دخول في المتصفح\n";
echo "5. افحص console المتصفح للأخطاء JavaScript\n";

echo "\n=== انتهى التشخيص ===\n";
