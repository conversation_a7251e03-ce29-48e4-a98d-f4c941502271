<?php

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== فحص روابط القائمة الجانبية ===\n\n";

// محاكاة تسجيل دخول البائع
$vendor = \Botble\Ecommerce\Models\Customer::where('is_vendor', true)->first();
if ($vendor) {
    auth('customer')->login($vendor);
    echo "✅ تم تسجيل دخول البائع: {$vendor->name}\n\n";
} else {
    echo "❌ لا يوجد بائع في النظام\n";
    exit;
}

// الحصول على عناصر القائمة
$menuItems = \Botble\Base\Facades\DashboardMenu::getAll('vendor');

echo "📋 عناصر القائمة المسجلة:\n";
foreach ($menuItems as $item) {
    if (!$item['name']) continue;
    
    $url = is_callable($item['url']) ? $item['url']() : $item['url'];
    $name = $item['name'];
    $id = $item['id'];
    
    echo "  🔗 {$name}\n";
    echo "     ID: {$id}\n";
    echo "     URL: {$url}\n";
    echo "     Active: " . ($item['active'] ? 'Yes' : 'No') . "\n\n";
}

// فحص المسارات المحددة
echo "🎯 فحص المسارات المحددة:\n";
$specificRoutes = [
    'marketplace.vendor.lessons.index' => 'الدروس',
    'marketplace.vendor.courses.index' => 'الدورات',
    'marketplace.vendor.services.index' => 'الخدمات',
    'marketplace.vendor.appointments.index' => 'المواعيد'
];

foreach ($specificRoutes as $routeName => $description) {
    try {
        $url = route($routeName);
        echo "  ✅ {$description}: {$url}\n";
        
        // فحص إذا كان المسار موجود في القائمة
        $foundInMenu = false;
        foreach ($menuItems as $item) {
            $itemUrl = is_callable($item['url']) ? $item['url']() : $item['url'];
            if ($itemUrl === $url) {
                $foundInMenu = true;
                break;
            }
        }
        echo "     في القائمة: " . ($foundInMenu ? 'نعم' : 'لا') . "\n";
        
    } catch (Exception $e) {
        echo "  ❌ {$description}: خطأ - {$e->getMessage()}\n";
    }
    echo "\n";
}

// فحص الترجمات
echo "🌐 فحص الترجمات:\n";
$translations = [
    'training-appointment::course-lesson.name' => 'اسم الدروس',
    'training-appointment::course.name' => 'اسم الدورات',
    'training-appointment::service.name' => 'اسم الخدمات',
    'training-appointment::appointment.name' => 'اسم المواعيد'
];

foreach ($translations as $key => $description) {
    $translated = __($key);
    echo "  📝 {$description}: {$translated}\n";
}

echo "\n=== تشخيص المشكلة ===\n";
echo "إذا كانت الروابط تظهر بشكل صحيح هنا ولكن لا تعمل في المتصفح،\n";
echo "فالمشكلة قد تكون في:\n";
echo "1. JavaScript يعترض النقرات\n";
echo "2. CSS يخفي الروابط أو يضع عنصر فوقها\n";
echo "3. Event listener يمنع التنقل\n";
echo "4. مشكلة في cache المتصفح\n\n";

echo "🔧 خطوات التشخيص:\n";
echo "1. افتح Developer Tools في المتصفح\n";
echo "2. اذهب لتبويب Console وابحث عن أخطاء JavaScript\n";
echo "3. اذهب لتبويب Network وراقب الطلبات عند النقر\n";
echo "4. افحص HTML للتأكد من وجود الروابط الصحيحة\n";
echo "5. جرب تعطيل JavaScript مؤقتاً\n";
