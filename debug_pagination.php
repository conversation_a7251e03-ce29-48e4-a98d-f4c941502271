<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Botble\TrainingAppointment\Models\Course;
use Botble\TrainingAppointment\Models\CourseCategory;
use Illuminate\Http\Request;

echo "=== Testing Pagination Links ===\n";

// Test the specific category that's causing issues
$category = CourseCategory::where('slug', 'Test-Category')->first();

if ($category) {
    echo "Category found: " . $category->name . "\n";
    
    $courses = Course::query()
        ->where('category_id', $category->id)
        ->where('status', 'published')
        ->with(['category', 'store'])
        ->paginate(12);
    
    echo "Courses paginator type: " . gettype($courses) . "\n";
    echo "Courses count: " . $courses->count() . "\n";
    
    // Test the pagination links
    try {
        $links = $courses->links();
        echo "Pagination links type: " . gettype($links) . "\n";
        
        if (is_array($links)) {
            echo "*** PAGINATION LINKS IS AN ARRAY! ***\n";
            echo "Array contents: " . json_encode($links) . "\n";
        } else {
            echo "Pagination links value: " . substr($links, 0, 100) . "...\n";
        }
        
        // Test htmlspecialchars on pagination links
        try {
            htmlspecialchars($links);
            echo "htmlspecialchars(pagination links): OK\n";
        } catch (TypeError $e) {
            echo "*** htmlspecialchars(pagination links): ERROR - " . $e->getMessage() . " ***\n";
        }
        
    } catch (Exception $e) {
        echo "Error getting pagination links: " . $e->getMessage() . "\n";
    }
    
    // Test other potential problematic values
    echo "\n=== Testing Other Values ===\n";
    
    // Test format_price with different values
    $testPrices = [0, 100, 1000.50, null, '', []];
    
    foreach ($testPrices as $price) {
        echo "\nTesting price: " . json_encode($price) . "\n";
        try {
            $formatted = format_price($price);
            echo "format_price result type: " . gettype($formatted) . "\n";
            
            if (is_array($formatted)) {
                echo "*** format_price returned an ARRAY! ***\n";
                echo "Array contents: " . json_encode($formatted) . "\n";
            }
            
            try {
                htmlspecialchars($formatted);
                echo "htmlspecialchars(format_price): OK\n";
            } catch (TypeError $e) {
                echo "*** htmlspecialchars(format_price): ERROR - " . $e->getMessage() . " ***\n";
            }
            
        } catch (Exception $e) {
            echo "format_price error: " . $e->getMessage() . "\n";
        }
    }
    
    // Test translation functions
    echo "\n=== Testing Translation Functions ===\n";
    
    $testKeys = ['Home', 'Courses', 'Free', 'View Details', 'No courses in this category'];
    
    foreach ($testKeys as $key) {
        try {
            $translation = __($key);
            echo "Translation '$key' type: " . gettype($translation) . "\n";
            
            if (is_array($translation)) {
                echo "*** Translation '$key' is an ARRAY! ***\n";
                echo "Array contents: " . json_encode($translation) . "\n";
            }
            
            try {
                htmlspecialchars($translation);
                echo "htmlspecialchars(translation '$key'): OK\n";
            } catch (TypeError $e) {
                echo "*** htmlspecialchars(translation '$key'): ERROR - " . $e->getMessage() . " ***\n";
            }
            
        } catch (Exception $e) {
            echo "Translation '$key' error: " . $e->getMessage() . "\n";
        }
    }
    
} else {
    echo "Category not found\n";
}