<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Botble\TrainingAppointment\Models\Course;
use Botble\TrainingAppointment\Models\CourseCategory;
use Illuminate\Http\Request;

echo "=== Simple Debug Test ===\n";

// Test the specific category that's causing issues
$category = CourseCategory::where('slug', 'Test-Category')->first();

if ($category) {
    echo "Category found: " . $category->name . "\n";
    
    $courses = Course::query()
        ->where('category_id', $category->id)
        ->where('status', 'published')
        ->with(['category', 'store'])
        ->paginate(12);
    
    echo "Courses count: " . $courses->count() . "\n";
    
    // Test the pagination links which might be the issue
    try {
        $links = $courses->links();
        echo "Pagination links type: " . gettype($links) . "\n";
        
        if (is_array($links)) {
            echo "*** PAGINATION LINKS IS AN ARRAY! This is the problem! ***\n";
            echo "Array contents: " . json_encode($links) . "\n";
        } else {
            echo "Pagination links is not an array, type: " . gettype($links) . "\n";
        }
        
        // Test htmlspecialchars on pagination links
        try {
            $result = htmlspecialchars($links);
            echo "htmlspecialchars(pagination links): SUCCESS\n";
        } catch (TypeError $e) {
            echo "*** FOUND THE PROBLEM! htmlspecialchars(pagination links): ERROR - " . $e->getMessage() . " ***\n";
            echo "Links value type: " . gettype($links) . "\n";
            if (is_object($links)) {
                echo "Links object class: " . get_class($links) . "\n";
            }
        }
        
    } catch (Exception $e) {
        echo "Error getting pagination links: " . $e->getMessage() . "\n";
    }
    
    // Test translation functions that might return arrays
    echo "\n=== Testing Translation Functions ===\n";
    
    $testKeys = ['Home', 'Courses', 'Free', 'View Details'];
    
    foreach ($testKeys as $key) {
        try {
            $translation = __($key);
            echo "Translation '$key' type: " . gettype($translation) . "\n";
            
            if (is_array($translation)) {
                echo "*** Translation '$key' is an ARRAY! This could be the problem! ***\n";
                echo "Array contents: " . json_encode($translation) . "\n";
            }
            
            try {
                htmlspecialchars($translation);
                echo "htmlspecialchars(translation '$key'): OK\n";
            } catch (TypeError $e) {
                echo "*** FOUND PROBLEM! htmlspecialchars(translation '$key'): ERROR - " . $e->getMessage() . " ***\n";
            }
            
        } catch (Exception $e) {
            echo "Translation '$key' error: " . $e->getMessage() . "\n";
        }
    }
    
} else {
    echo "Category not found\n";
}