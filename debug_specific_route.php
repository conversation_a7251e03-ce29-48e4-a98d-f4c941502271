<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Botble\TrainingAppointment\Models\Course;
use Botble\TrainingAppointment\Models\CourseCategory;
use Illuminate\Http\Request;

echo "=== Debugging Specific Route Issue ===\n";

// Test the specific category that's causing issues
$category = CourseCategory::where('slug', 'Test-Category')->first();

if (!$category) {
    echo "Category 'Test-Category' not found, trying lowercase...\n";
    $category = CourseCategory::where('slug', 'test-category')->first();
}

if ($category) {
    echo "Category found: " . $category->id . "\n";
    echo "Category slug: " . $category->slug . "\n";
    
    // Check each field type and value
    $fields = ['name', 'description'];
    
    foreach ($fields as $field) {
        $value = $category->$field;
        $type = gettype($value);
        echo "Category $field type: $type\n";
        
        if (is_array($value)) {
            echo "Category $field is an ARRAY: " . json_encode($value) . "\n";
            echo "This is the problem!\n";
        } else {
            echo "Category $field value: " . $value . "\n";
        }
    }
    
    // Get courses for this category
    $courses = Course::where('category_id', $category->id)
        ->where('status', 'published')
        ->with(['category', 'store'])
        ->get();
    
    echo "\nFound " . $courses->count() . " courses\n";
    
    foreach ($courses as $course) {
        echo "\nCourse ID: " . $course->id . "\n";
        
        $courseFields = ['title', 'description', 'content'];
        
        foreach ($courseFields as $field) {
            $value = $course->$field;
            $type = gettype($value);
            echo "Course $field type: $type\n";
            
            if (is_array($value)) {
                echo "Course $field is an ARRAY: " . json_encode($value) . "\n";
                echo "This is the problem!\n";
            } else {
                echo "Course $field value: " . substr($value, 0, 50) . "...\n";
            }
        }
        
        // Test the specific operations that might fail
        echo "\nTesting operations that might fail:\n";
        
        try {
            $titleOutput = htmlspecialchars($course->title);
            echo "htmlspecialchars(course->title): OK\n";
        } catch (Exception $e) {
            echo "htmlspecialchars(course->title): ERROR - " . $e->getMessage() . "\n";
        }
        
        try {
            $descLimit = Str::limit($course->description, 100);
            echo "Str::limit(course->description): OK\n";
        } catch (Exception $e) {
            echo "Str::limit(course->description): ERROR - " . $e->getMessage() . "\n";
        }
        
        // Test category name
        if ($course->category) {
            try {
                $categoryName = htmlspecialchars($course->category->name);
                echo "htmlspecialchars(course->category->name): OK\n";
            } catch (Exception $e) {
                echo "htmlspecialchars(course->category->name): ERROR - " . $e->getMessage() . "\n";
            }
        }
    }
} else {
    echo "Category not found\n";
}

echo "\n=== Raw Database Check ===\n";

// Check raw database values
$rawCategory = \DB::table('ta_course_categories')->where('slug', 'Test-Category')->first();
if (!$rawCategory) {
    $rawCategory = \DB::table('ta_course_categories')->where('slug', 'test-category')->first();
}

if ($rawCategory) {
    echo "Raw category name type: " . gettype($rawCategory->name) . "\n";
    echo "Raw category name value: " . $rawCategory->name . "\n";
    echo "Raw category description type: " . gettype($rawCategory->description) . "\n";
    echo "Raw category description value: " . $rawCategory->description . "\n";
}