<?php

require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';

// Boot the application
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;

echo "=== Translation Debug Script ===\n";

// Clear all caches first
try {
    Artisan::call('cache:clear');
    Artisan::call('config:clear');
    Artisan::call('view:clear');
    echo "Caches cleared successfully\n";
} catch (Exception $e) {
    echo "Cache clear error: " . $e->getMessage() . "\n";
}

// Set locale to Arabic
App::setLocale('ar');
echo "Current locale: " . App::getLocale() . "\n";

// Test different ways to get the translation
echo "\n=== Testing Translation Methods ===\n";

// Method 1: Direct __() function
try {
    $courses1 = __('Courses');
    echo "__('Courses') type: " . gettype($courses1) . "\n";
    if (is_array($courses1)) {
        echo "__('Courses') array content: " . print_r($courses1, true) . "\n";
    } else {
        echo "__('Courses') value: " . $courses1 . "\n";
    }
} catch (Exception $e) {
    echo "Error with __('Courses'): " . $e->getMessage() . "\n";
}

// Method 2: Lang::get()
try {
    $courses2 = Lang::get('Courses');
    echo "Lang::get('Courses') type: " . gettype($courses2) . "\n";
    if (is_array($courses2)) {
        echo "Lang::get('Courses') array content: " . print_r($courses2, true) . "\n";
    } else {
        echo "Lang::get('Courses') value: " . $courses2 . "\n";
    }
} catch (Exception $e) {
    echo "Error with Lang::get('Courses'): " . $e->getMessage() . "\n";
}

// Method 3: trans() function
try {
    $courses3 = trans('Courses');
    echo "trans('Courses') type: " . gettype($courses3) . "\n";
    if (is_array($courses3)) {
        echo "trans('Courses') array content: " . print_r($courses3, true) . "\n";
    } else {
        echo "trans('Courses') value: " . $courses3 . "\n";
    }
} catch (Exception $e) {
    echo "Error with trans('Courses'): " . $e->getMessage() . "\n";
}

// Check specific translation files
echo "\n=== Checking Translation Files ===\n";

// Check main courses.php
$coursesFile = __DIR__ . '/lang/ar/courses.php';
if (file_exists($coursesFile)) {
    $coursesTranslations = include $coursesFile;
    echo "Main courses.php exists\n";
    if (isset($coursesTranslations['Courses'])) {
        echo "'Courses' in main file type: " . gettype($coursesTranslations['Courses']) . "\n";
        echo "'Courses' in main file value: " . print_r($coursesTranslations['Courses'], true) . "\n";
    } else {
        echo "'Courses' key not found in main courses.php\n";
    }
} else {
    echo "Main courses.php not found\n";
}

// Check plugin courses.php
$pluginCoursesFile = __DIR__ . '/lang/vendor/plugins/training-appointment/ar/course.php';
if (file_exists($pluginCoursesFile)) {
    $pluginTranslations = include $pluginCoursesFile;
    echo "Plugin course.php exists\n";
    if (isset($pluginTranslations['Courses'])) {
        echo "'Courses' in plugin file type: " . gettype($pluginTranslations['Courses']) . "\n";
        echo "'Courses' in plugin file value: " . print_r($pluginTranslations['Courses'], true) . "\n";
    } else {
        echo "'Courses' key not found in plugin course.php\n";
    }
} else {
    echo "Plugin course.php not found\n";
}

// Test with specific namespace
echo "\n=== Testing Namespaced Translations ===\n";
try {
    $namespacedCourses = __('plugins/training-appointment::course.Courses');
    echo "Namespaced translation type: " . gettype($namespacedCourses) . "\n";
    if (is_array($namespacedCourses)) {
        echo "Namespaced translation array: " . print_r($namespacedCourses, true) . "\n";
    } else {
        echo "Namespaced translation value: " . $namespacedCourses . "\n";
    }
} catch (Exception $e) {
    echo "Error with namespaced translation: " . $e->getMessage() . "\n";
}

echo "\n=== Debug Complete ===\n";
